<template>
  <q-dialog v-model="popupConfirmItems.isPopup" persistent>
    <q-card class=" tw:w-[70rem] tw:max-w-[70rem]" :style="{ minWidth: minWidthX2 }">
      <div class="tw:flex tw:flex-col">
        <div class="tw:font-[700] tw:text-m-design tw:bg-[#004AB9] tw:text-white tw:p-5">
          {{ titlePopup }}</div>
        <div class="tw:mb-4 tw:text-m-design tw:font-[400] tw:p-5">
          {{ captionPopup }}
        </div>
        <div class="tw:p-5 tw:pt-0">
          <div class="tw:border tw:border-[#D2D2D2] tw:rounded-none">
            <div class="tw:flex tw:flex-col tw:divide-y tw:divide-[#D2D2D2]">
              <div v-for="item in popupConfirmItems.listItems" :key="item" class="tw:flex tw:tl:flex-row tw:flex-col">
                <div :class="`${item?.hiddenKey ? 'tw:invisible ' : ''} tw:tl:w-[30%]
          tw:bg-[#E2E3EA] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design
          tw:items-center tw:flex tw:justify-between`" :style="{ minWidth }">
                  {{ item?.key }}
                  <q-badge v-if="item?.badge" class=" tw:text-white tw:bg-[#E80F00] tw:p-1 tw:rounded tw:text-xxs-design tw:justify-center
              tw:items-center tw:flex tw:tl:ml-1">
                    必須
                  </q-badge>
                </div>
                <div class="tw:tl:w-[70%] tw:p-2 tw:font-[400] tw:text-m-design tw:flex tw:items-center tw:break-all">
                  <p class="tw:pl-2" v-if="item.vHtml" v-html="clearHTML(item.value)"></p>
                  <span v-else class="tw:pl-2">{{ item?.value }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="tw:flex tw:flex-col tw:tl:flex-row tw:justify-end tw:p-5 tw:pt-0 tw:gap-2">
          <BaseButton class="tw:flex tw:justify-center tw:items-center tw:mr-4 tw:rounded-full
              tw:font-[700] tw:text-xs-design tw:tl:w-[20rem] tw:w-full
              " :class="`tw:text-[#004AB9]`" outline label="入力内容を修正する" @click.prevent="closeModal" />
          <BaseButton type="button" class="tw:flex tw:justify-center tw:items-center tw:text-white tw:font-[700]
              tw:tl:w-[20rem] tw:w-full tw:rounded-full
              tw:text-xs-design" :class="`tw:bg-[#004AB9]`" @click.prevent="acceptModal" label="登録する" />
        </div>
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import {
  inject, onMounted, ref, onBeforeUnmount,
} from 'vue';
import { clearHTML } from 'helpers/common';

import BaseButton from './base/vs/BaseButton.vue';

const { colorMain, colorSub } = storeToRefs(useAppStore());
const popupConfirmItems = inject('popupConfirmItems');
const {
  titlePopup = '',
  captionPopup = '',
  confirmFunc,
  minWidthDefault = 128,
  minWidthTlDefault = 128,
  minWidthDtDefault = 128,
} = popupConfirmItems.value;
const minWidth = ref();
const minWidthX2 = ref();
const closeModal = () => {
  popupConfirmItems.value.isPopup = false;
};

const acceptModal = () => {
  popupConfirmItems.value.isPopup = false;
  if (confirmFunc) { confirmFunc(); }
};

const updateBreakpoint = () => {
  if (window.innerWidth < 960) {
    minWidth.value = `${minWidthDefault}px`;
    minWidthX2.value = `${minWidthDefault * 2.1}px`;
  } else if (window.innerWidth >= 960 && window.innerWidth < 1440) {
    minWidth.value = `${minWidthTlDefault}px`;
    minWidthX2.value = `${minWidthTlDefault * 2.1}px`;
  } else {
    minWidth.value = `${minWidthDtDefault}px`;
    minWidthX2.value = `${minWidthDtDefault * 2.1}px`;
  }
};
onMounted(() => {
  window.addEventListener('resize', updateBreakpoint);
  updateBreakpoint(); // Set initial value
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateBreakpoint);
});

</script>

<style scoped>
:deep(a) {
  color: #007bff !important;
}
</style>
