import BaseService from 'services/base.service';
import { useAppStore } from 'stores/app-store';
import { useAuthStore } from 'stores/auth-store';

class AuthService extends BaseService {
  async login(params) {
    const auth = useAuthStore();
    const { setRole } = useAppStore();
    const loginResult = await this.dao.login(params);
    const { code, payload } = loginResult;
    if (code === 0) {
      auth.signIn(payload);
      setRole(payload);
    }
    return payload;
  }

  /**
   * Login admin site
   * @param {*} params login infomation
   * @returns
   */
  async adminLogin(params) {
    const auth = useAuthStore();
    const { setRole } = useAppStore();
    const loginResult = await this.dao.adminLogin(params);
    const { code, payload } = loginResult;
    if (code === 0) {
      auth.signIn(payload);
      setRole(payload);
    }
    return payload;
  }

  /**
   * Admin login proxy user to get token
   * @param {*} params login information { user_code, password }
   * @returns token proxy user
   */

  async loginLink(params) {
    const { signInLink } = useAuthStore();
    const loginResult = await this.dao.loginLink(params);
    const { code, payload } = loginResult;
    if (code === 0) {
      signInLink(payload);
    }
    return loginResult;
  }

  async checkLogin() {
    const { setRole } = useAppStore();
    const auth = useAuthStore();
    try {
      const checkLoginResult = await this.dao.checkLogin();
      const { code, payload } = checkLoginResult;
      if (code === 0) {
        auth.signIn(payload);
        setRole(payload);
      }
    } catch (ex) {
      auth.signOut();
    }
  }

  async loginInsteadLink(body) {
    const { signInProxyUser } = useAuthStore();
    const loginInsteadResult = await this.dao.loginInsteadLink(body);
    const { code, payload } = loginInsteadResult;
    if (code === 0) {
      signInProxyUser(payload);
    }
    return loginInsteadResult;
  }

  async logout() {
    const auth = useAuthStore();
    const { setRole } = useAppStore();
    setRole({});
    auth.signOut();
  }

  async sendSmsPassword(body) {
    await this.dao.sendSmsPassword(body);
  }

  async resetPassword(body, token) {
    await this.dao.resetPassword(body, token);
  }

  async recoveryPassword(body) {
    await this.dao.recoveryPassword(body);
    const auth = useAuthStore();
    const { setRole } = useAppStore();
    setRole({});
    auth.signOut();
  }
}

export default new AuthService('auth');
