import MESSAGE from 'helpers/message';

const editShipping = {
  additionalProperties: false,
  type: 'object',
  required: [ 'grossWeight', 'date'],
  properties: {
    destinationId: {
      type: 'integer',
      minimum: 1,
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    grossWeight: {
      type: 'number',
      minLength: 1,
      exclusiveMinimum: 0,
      maximum: 9999999.99,
      errorMessage: {
        exclusiveMinimum: MESSAGE.MSG_LIMITS_GROSSWEIGHT_MIN_ERROR,
        maximum: MESSAGE.MSG_SAGE_NUM_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    tareWeight: {
      type: 'number',
      exclusiveMaximum: {
        $data: '1/grossWeight',
      },
      errorMessage: {
        exclusiveMaximum: MESSAGE.MSG_LIMITS_TAREWEIGHT_MAX_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    netWeight: {
      type: 'string',
    },
    date: {
      type: 'string',
      format: 'slash-date',
      errorMessage: {
        format: MESSAGE.MSG_LIMITS_DATE_ERROR,
      },
    },
  },
  errorMessage: {
    required: {
      name: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
    },
  },
};

export default editShipping;
