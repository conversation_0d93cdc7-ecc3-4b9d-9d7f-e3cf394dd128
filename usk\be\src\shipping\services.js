const BaseService = require('../base/serviceFn');
const { ControlledException } = require('../base/errors');
const { MESSAGE } = require('../utils/message');
const { Prisma } = require('@prisma/client');
const dayjs = require('../boot/dayjs');
const {
  LIMIT_EXPORT,
  ROLES_ENUM,
  SHIPPING_TYPE_ENUM,
  ENTERPRISE_TYPE_ENUM,
  STAFF_TYPE_ENUM,
  TOTAL_LIMIT_EXPORT,
  INVENTORY_HISTORY_TYPE_ENUM,
  ADMIN_SYSTEM_SETTING_KEYS_ENUM,
} = require('../helpers/enum');

class ShippingService extends BaseService {
  // ====== 1. Private method ==========
  async #checkCanEditShipping(shippingId, user) {
    const connect = this.DB.READ;
    const data = await connect.the_origins.findFirst({
      where: {
        id: shippingId,
        delete_flag: false,
        starting_enterprise_id: user.enterprise_id,
        starting_user: {
          // only starting user can edit
          id: user.id,
        },
      },
      select: {
        arrival_date: true,
        shipping_type: true,
        created_on: true,
        destination_enterprise: {
          select: {
            type: true,
          },
        },
      },
    });
    const systemSetting = await connect.sys_settings.findFirst({
      where: {
        setting_name: ADMIN_SYSTEM_SETTING_KEYS_ENUM.EDIT_DEADLINE_FOR_ARRIVAL,
        delete_flag: false,
      },
      select: {
        setting_value: true,
      },
    });
    if (!data || data.shipping_type === SHIPPING_TYPE_ENUM.PROXY || data.arrival_date) {
      return false;
    } else if (
      dayjs(data.created_on)
        .add(
          Number(systemSetting?.setting_value ? systemSetting.setting_value * 24 : 14 * 24),
          'hour'
        )
        .isBefore(dayjs().toDate())
    ) {
      return false;
    }
    return true;
  }

  async #checkCanDelShipping(shippingId, user) {
    const connect = this.DB.READ;
    const data = await connect.the_origins.findFirst({
      where: {
        id: shippingId,
        delete_flag: false,
        starting_enterprise_id: user.enterprise_id,
        starting_user: {
          // only starting user can delete
          id: user.id,
        },
      },
      select: {
        arrival_date: true,
        shipping_type: true,
        created_on: true,
        ingredient: true,
        destination_enterprise: {
          select: {
            type: true,
          },
        },
      },
    });

    const systemSetting = await connect.sys_settings.findFirst({
      where: {
        setting_name: ADMIN_SYSTEM_SETTING_KEYS_ENUM.EDIT_DEADLINE_FOR_ARRIVAL,
        delete_flag: false,
      },
      select: {
        setting_value: true,
      },
    });

    if (!data) {
      return false;
    }

    // if the destination enterprise is foreign, then can delete
    if (data.destination_enterprise.type === ENTERPRISE_TYPE_ENUM.FOREIGN) {
      return true;
    }

    // if the shipment is already arrived, then cannot delete
    if (data.arrival_date) {
      return false;
    }
    // if the shipment is created more than 14 days, then cannot delete
    if (
      dayjs(data.created_on)
        .add(
          Number(systemSetting?.setting_value ? systemSetting.setting_value * 24 : 14 * 24),
          'hour'
        )
        .isBefore(dayjs().toDate())
    ) {
      return false;
    }
    return true;
  }

  // Calculate actual shipping date
  #calcShippingDate(shippingDate) {
    const currentDateTime = dayjs.getDate();
    const shippingDateTime = dayjs.getDateFromJST(`${shippingDate} 23:59:59`);
    // Check if shippingDate is in the past
    if (shippingDateTime.isBefore(currentDateTime)) {
      return { dateTimeNow: currentDateTime, shippingDateTime };
    }

    return { dateTimeNow: currentDateTime, shippingDateTime: currentDateTime };
  }

  // ====== 2. Public method ==========
  async getShippingList(user, query) {
    const {
      page,
      limit,
      destination,
      startDate,
      endDate,
      licenseNumber,
      note1,
      note2,
      code,
      enterpriseName,
    } = query;
    const connect = this.DB.READ;

    const searchCondition = {
      delete_flag: false,
      starting_enterprise_id: user.enterprise_id,
      shipping_net_weight: {
        not: null,
      },
      starting_user_id: user.staff_type === STAFF_TYPE_ENUM.STAFF ? user.id : undefined,
      destination_license_number: licenseNumber
        ? {
          contains: licenseNumber,
        }
        : undefined,
      destination_user_note_1: note1
        ? {
          contains: note1,
        }
        : undefined,
      destination_user_note_2: note2
        ? {
          contains: note2,
        }
        : undefined,
      destination_user_name: destination
        ? {
          contains: destination,
        }
        : undefined,
      destination_enterprise_name: enterpriseName
        ? {
          contains: enterpriseName,
        }
        : undefined,
      destination_enterprise: code
        ? {
          OR: [
            {
              enterprise_code: {
                contains: code.replace(/\s+/g, ''),
              },
            },
          ],
        }
        : undefined,
      shipping_date: {
        gte: startDate ? dayjs.getDateFromJST(`${startDate} 00:00:00`).toDate() : undefined,
        lte: endDate ? dayjs.getDateFromJST(`${endDate} 23:59:59`).toDate() : undefined,
      },
    };

    const totalCount = await connect.the_origins.count({
      where: searchCondition,
    });

    if (totalCount > TOTAL_LIMIT_EXPORT) {
      throw new ControlledException(MESSAGE.MSG_TOO_MANY_RESULTS);
    }

    if (totalCount === 0) {
      return this.SUCCESS({
        items: [],
        total_item: 0,
        page: 1,
        page_size: +limit,
      });
    }

    const offset = (+page - 1) * +limit;
    let tempPage = +page;

    // if offset is greater than total item, recalculate page and offset
    if (offset >= totalCount) {
      tempPage = Math.ceil(totalCount / +limit);
    }

    const data = await connect.the_origins.findMany({
      where: searchCondition,
      select: {
        id: true,
        shipping_date: true,
        code: true,
        shipping_net_weight: true,
        starting_user_name: true,
        starting_license_number: true,
        starting_enterprise_name: true,
        destination_user_name: true,
        destination_license_number: true,
        destination_enterprise_name: true,
        shipping_type: true,
        destination_enterprise: {
          select: {
            enterprise_name: true,
            enterprise_code: true,
          },
        },
        destination_user: {
          select: {
            user_code: true,
            name: true,
            role: true,
            staff_type: true,
            enterprise_type: true,
          },
        },
        starting_enterprise: {
          select: {
            enterprise_name: true,
            enterprise_code: true,
          },
        },
        starting_user: {
          select: {
            license_number: true,
            role: true,
            name: true,
            staff_type: true,
            enterprise_type: true,
          },
        },
      },
      orderBy: { shipping_date: 'desc' },
    });
    return this.SUCCESS({
      items: data,
      total_item: totalCount,
      page: tempPage,
      page_size: +limit,
    });
  }

  async getShippingDetail(params) {
    const { user, id } = params;
    const connect = this.DB.READ;

    // Validate id parameter
    if (!id || isNaN(+id) || +id <= 0) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO, {}, 400);
    }
    const data = await connect.the_origins.findFirst({
      where: {
        delete_flag: false,
        id: +id,
        starting_enterprise_id: user.enterprise_id,
        starting_user: {
          id: user.staff_type === STAFF_TYPE_ENUM.STAFF ? user.id : undefined,
        },
      },
      select: {
        qr_code: true,
        code: true,
        shipping_net_weight: true,
        shipping_gross_weight: true,
        shipping_tare_weight: true,
        shipping_quantity: true,
        shipping_date: true,
        arrival_date: true,
        shipping_type: true,
        starting_user_name: true,
        starting_license_number: true,
        starting_enterprise_name: true,
        destination_user_name: true,
        destination_license_number: true,
        destination_enterprise_name: true,
        destination_user_note_1: true,
        destination_user_note_2: true,
        starting_user_note_1: true,
        starting_user_note_2: true,
        shipping_reason_diff: true,
        shipping_id_list: true,
        arrival_gross_weight: true,
        starting_user: {
          select: {
            role: true,
            user_code: true,
            enterprise_type: true,
            staff_type: true,
          },
        },
        destination_enterprise: {
          select: {
            type: true,
          },
        },
        destination_user_id: true,
        destination_user: {
          select: {
            enterprise_type: true,
            staff_type: true,
          },
        },
        ingredient: true,
      },
    });
    if (!data) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO);
    }

    // check can edit or delete
    const canEdit = await this.#checkCanEditShipping(+id, user);
    const canDel = await this.#checkCanDelShipping(+id, user);
    const foreignFlag = data.destination_enterprise.type === ENTERPRISE_TYPE_ENUM.FOREIGN;

    // get inventory information - {the_origin_codes, supplier_name}
    let inventory_info_codes = [];

    if (data.shipping_id_list[0] && data.shipping_id_list.length) {
      const lstId = [];
      data.shipping_id_list.forEach((value) => {
        if (value.the_origins) {
          value.the_origins.forEach((o) => lstId.push(o.id));
        }
      });
      const inventoryList = await connect.the_origins.findMany({
        where: {
          id: {
            in: lstId,
          },
          delete_flag: false,
          arrival_date: {
            not: null,
          },
        },
        select: {
          id: true,
          code: true,
          destination_enterprise_name: true,
          destination_user_name: true,
        },
      });

      // calculate the code and supplier name
      inventoryList.forEach((inventory) => {
        inventory_info_codes.push({
          code: inventory.code,
          supplier_name: inventory.destination_enterprise_name,
        });
      });

      delete data.ingredient;
    }

    return this.SUCCESS({
      ...data,
      can_edit: canEdit,
      can_del: !!canDel,
      foreign_flag: foreignFlag,
      inventory_info_codes: inventory_info_codes.length ? inventory_info_codes : undefined,
    });
  }

  async cancelShipping(params) {
    const { id, user } = params;
    const connectWrite = this.DB.WRITE;

    // Validate id parameter
    if (!id || isNaN(+id) || +id <= 0) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO);
    }
    const canDel = await this.#checkCanDelShipping(+id, user);
    if (!canDel) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO);
    }

    const theOrigin = await connectWrite.the_origins.findFirst({
      where: {
        id: +id,
      },
      include: {
        destination_enterprise: true,
      }
    });

    const dateNow = dayjs().toDate();
    const lstHistory = [];
    const bodyUpdateInventory = [];

    if (theOrigin.ingredient) {
      const ingredient = await connectWrite.inventories.findMany({
        where: {
          id: {
            in: theOrigin.ingredient.map((item) => item.shipping_inventory_id),
          },
        },
      });

      ingredient.forEach((item) => {
        const info = theOrigin.ingredient.find((i) => i.shipping_inventory_id === item.id);
        lstHistory.push({
          inventory_id: item.id,
          group_name: item.group_name,
          gross_weight_inventory: item.gross_weight_inventory,
          new_tare_weight_inventory: (+info.tare_weight_inventory || 0) + (+item.tare_weight || 0),
          net_weight_inventory: item.net_weight_inventory,
          new_gross_weight_inventory: (+item.gross_weight_inventory || 0) + (+info.gross_weight || 0),
          tare_weight_inventory: item.tare_weight_inventory,
          new_net_weight_inventory:
            (+item.net_weight_inventory || 0) + (+info.taken_inventory_weight || 0),
          net_weight_total: item.net_weight_total,
          latest_arrival_date: dateNow,
          created_by_id: user.id,
          latest_updated_by_id: user.id,
          created_on: dateNow,
          latest_updated_on: dateNow,
          user_id: item.user_id,
          type_diff: 10,
          current_arrival_id_list: item.current_arrival_id_list,
          new_current_arrival_id_list: item.current_arrival_id_list.filter((i) => i.id !== +id),
          is_display_inventories_history: false,
        });
        bodyUpdateInventory.push({
          where: {
            id: item.id,
          },
          data: {
            net_weight_inventory: {
              increment: info.taken_inventory_weight,
            },
            gross_weight_inventory: {
              increment: info.gross_weight,
            },
            tare_weight_inventory: {
              increment: info.tare_weight,
            },
            is_history_cancel_locked: true,
          },
        });
      });
    }

    await connectWrite.$transaction(
      async (tx) => {
        await tx.the_origins.update({
          where: {
            id: +id,
          },
          data: {
            delete_flag: true,
            latest_updated_by_id: user.id,
            latest_updated_on: dayjs().toDate(),
            arrival_gross_weight: theOrigin.destination_enterprise.type === ENTERPRISE_TYPE_ENUM.FOREIGN ? 0 : undefined,
            arrival_tare_weight: theOrigin.destination_enterprise.type === ENTERPRISE_TYPE_ENUM.FOREIGN ? 0 : undefined,
            arrival_net_weight: theOrigin.destination_enterprise.type === ENTERPRISE_TYPE_ENUM.FOREIGN ? 0 : undefined,
            arrival_quantity: theOrigin.destination_enterprise.type === ENTERPRISE_TYPE_ENUM.FOREIGN ? 0 : undefined,
          },
        });

        await tx.the_origins_history.create({
          data: {
            the_origins_history_type: INVENTORY_HISTORY_TYPE_ENUM.DELETE,
            the_origins_id: theOrigin.id,
            the_origins_history_created_by_id: user.id,
            the_origins_history_created_on: dateNow,
            code: theOrigin.code,
            shipping_date: theOrigin.shipping_date,
            shipping_gross_weight: theOrigin.shipping_gross_weight,
            shipping_tare_weight: theOrigin.shipping_tare_weight,
            shipping_net_weight: theOrigin.shipping_net_weight,
            shipping_quantity: theOrigin.shipping_quantity,
            arrival_gross_weight: theOrigin.arrival_gross_weight,
            arrival_tare_weight: theOrigin.arrival_tare_weight,
            arrival_net_weight: theOrigin.arrival_net_weight,
            arrival_quantity: theOrigin.arrival_quantity,
            arrival_date: theOrigin.arrival_date,
            type_diff: theOrigin.type_diff,
            shipping_type: theOrigin.shipping_type,
            reason_diff: theOrigin.reason_diff,
            created_by_id: theOrigin.created_by_id,
            latest_updated_by_id: theOrigin.latest_updated_by_id,
            created_on: theOrigin.created_on,
            latest_updated_on: theOrigin.latest_updated_on,
            delete_flag: theOrigin.delete_flag,
            qr_code: theOrigin.qr_code,
            destination_enterprise_id: theOrigin.destination_enterprise_id,
            starting_enterprise_id: theOrigin.starting_enterprise_id,
            inventory_id: theOrigin.inventory_id,
            ingredient: theOrigin.ingredient,
            setting: theOrigin.setting,
            destination_user_id: theOrigin.destination_user_id,
            starting_user_id: theOrigin.starting_user_id,
            shipping_type_diff: theOrigin.shipping_type_diff,
            shipping_reason_diff: theOrigin.shipping_reason_diff,
            destination_enterprise_name: theOrigin.destination_enterprise_name,
            destination_user_name: theOrigin.destination_user_name,
            destination_license_number: theOrigin.destination_license_number,
            destination_user_note_1: theOrigin.destination_user_note_1,
            destination_user_note_2: theOrigin.destination_user_note_2,
            starting_enterprise_name: theOrigin.starting_enterprise_name,
            starting_user_name: theOrigin.starting_user_name,
            starting_license_number: theOrigin.starting_license_number,
            starting_user_note_1: theOrigin.starting_user_note_1,
            starting_user_note_2: theOrigin.starting_user_note_2,
            shipping_inventory_type: theOrigin.shipping_inventory_type,
            shipping_id_list: theOrigin.shipping_id_list,
          }
        });

        await Promise.all(
          lstHistory.map((item) => {
            return tx.inventories_history.create({
              data: item,
            });
          })
        );
        await Promise.all(
          bodyUpdateInventory.map((item) => {
            return tx.inventories.update(item);
          })
        );
      },
      { isolationLevel: Prisma.TransactionIsolationLevel.Serializable }
    );

    return this.SUCCESS({ message: MESSAGE.MSG_CANCEL_SHIPPING_INFO });
  }

  async editShipping(params) {
    const { id, user, ...body } = params;
    const connectRead = this.DB.READ;
    const connectWrite = this.DB.WRITE;

    // Validate id parameter
    if (!id || isNaN(+id) || +id <= 0) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO, {}, 400);
    }
    const {
      shippingGrossWeight,
      shippingTareWeight,
      shippingDate,
      destinationId,
      reasonDiff,
      typeDiff,
    } = body;

    const shippingNetWeight = +shippingGrossWeight - +(shippingTareWeight || 0);

    const { shippingDateTime, dateTimeNow } = this.#calcShippingDate(shippingDate);

    const canEdit = await this.#checkCanEditShipping(+id, user);
    if (!canEdit) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO);
    }
    const checkOrigin = await connectRead.the_origins.findFirst({
      where: {
        id: +id,
        delete_flag: false,
        starting_enterprise_id: user.enterprise_id,
        starting_user: {
          id: user.staff_type === STAFF_TYPE_ENUM.STAFF ? user.id : undefined,
        },
      },
      include: {
        destination_enterprise: true,
      },
    });
    const setting = await connectRead.settings.findFirst({
      where: {
        user_id: user.id,
      },
      select: {
        display_shipment_weight: true,
        unit_per_gram: true,
      },
    });
    const destination = await connectRead.users.findFirst({
      where: {
        id: destinationId,
        delete_flag: false,
      },
      select: {
        id: true,
        enterprise_id: true,
        enterprise: {
          select: {
            enterprise_name: true,
          },
        },
        name: true,
        note_1: true,
        note_2: true,
        license_number: true,
      },
    });
    if (
      [ENTERPRISE_TYPE_ENUM.FOREIGN, ENTERPRISE_TYPE_ENUM.FARM].includes(
        checkOrigin.destination_enterprise.type
      ) &&
      checkOrigin.destination_user_id !== destinationId
    ) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO);
    }
    if (!checkOrigin || !setting || !destination) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO);
    }
    const codeFirst = checkOrigin.code.slice(0, 7);
    const codeEnd = checkOrigin.code.slice(13);
    let bodyUpdate = {
      shipping_gross_weight: shippingGrossWeight,
      shipping_tare_weight: shippingTareWeight,
      shipping_net_weight: shippingNetWeight,
      shipping_date: shippingDateTime,
      destination_enterprise_id: destination.enterprise_id,
      destination_user_id: destinationId,
      destination_enterprise_name: destination.enterprise.enterprise_name,
      destination_user_name: destination.name,
      destination_license_number: destination.license_number,
      destination_user_note_1: destination.note_1,
      destination_user_note_2: destination.note_2,
      latest_updated_by_id: user.id,
      latest_updated_on: dateTimeNow.toDate(),
      code: `${codeFirst}${dayjs(shippingDate).format('YYMMDD')}${codeEnd}`,
      shipping_type_diff: typeDiff,
      shipping_reason_diff: reasonDiff,
    };
    if (
      [ENTERPRISE_TYPE_ENUM.FOREIGN, ENTERPRISE_TYPE_ENUM.FARM].includes(
        checkOrigin.destination_enterprise.type
      )
    ) {
      bodyUpdate = {
        shipping_date: shippingDateTime.toDate(),
        shipping_gross_weight: shippingGrossWeight,
        shipping_tare_weight: shippingTareWeight,
        shipping_net_weight: shippingNetWeight,
        arrival_gross_weight: shippingGrossWeight,
        arrival_tare_weight: shippingTareWeight,
        arrival_net_weight: shippingNetWeight,
        latest_updated_by_id: user.id,
        latest_updated_on: dateTimeNow.toDate(),
        shipping_type_diff: typeDiff,
        shipping_reason_diff: reasonDiff,
        code: `${codeFirst}${dayjs(shippingDate).format('YYMMDD')}${codeEnd}`,
      };
    }
    await connectWrite.$transaction(async (tx) => {
      await tx.the_origins_history.create({
        data: {
          the_origins_history_type: INVENTORY_HISTORY_TYPE_ENUM.UPDATE,
          the_origins_id: checkOrigin.id,
          the_origins_history_created_by_id: user.id,
          the_origins_history_created_on: dateTimeNow.toDate(),
          code: checkOrigin.code,
          shipping_date: checkOrigin.shipping_date,
          shipping_gross_weight: checkOrigin.shipping_gross_weight,
          shipping_tare_weight: checkOrigin.shipping_tare_weight,
          shipping_net_weight: checkOrigin.shipping_net_weight,
          shipping_quantity: checkOrigin.shipping_quantity,
          arrival_gross_weight: checkOrigin.arrival_gross_weight,
          arrival_tare_weight: checkOrigin.arrival_tare_weight,
          arrival_net_weight: checkOrigin.arrival_net_weight,
          arrival_quantity: checkOrigin.arrival_quantity,
          arrival_date: checkOrigin.arrival_date,
          type_diff: checkOrigin.type_diff,
          shipping_type: checkOrigin.shipping_type,
          reason_diff: checkOrigin.reason_diff,
          created_by_id: checkOrigin.created_by_id,
          latest_updated_by_id: checkOrigin.latest_updated_by_id,
          created_on: checkOrigin.created_on,
          latest_updated_on: checkOrigin.latest_updated_on,
          delete_flag: checkOrigin.delete_flag,
          qr_code: checkOrigin.qr_code,
          destination_enterprise_id: checkOrigin.destination_enterprise_id,
          starting_enterprise_id: checkOrigin.starting_enterprise_id,
          inventory_id: checkOrigin.inventory_id,
          ingredient: checkOrigin.ingredient,
          setting: checkOrigin.setting,
          destination_user_id: checkOrigin.destination_user_id,
          starting_user_id: checkOrigin.starting_user_id,
          shipping_type_diff: checkOrigin.shipping_type_diff,
          shipping_reason_diff: checkOrigin.shipping_reason_diff,
          destination_enterprise_name: checkOrigin.destination_enterprise_name,
          destination_user_name: checkOrigin.destination_user_name,
          destination_license_number: checkOrigin.destination_license_number,
          destination_user_note_1: checkOrigin.destination_user_note_1,
          destination_user_note_2: checkOrigin.destination_user_note_2,
          starting_enterprise_name: checkOrigin.starting_enterprise_name,
          starting_user_name: checkOrigin.starting_user_name,
          starting_license_number: checkOrigin.starting_license_number,
          starting_user_note_1: checkOrigin.starting_user_note_1,
          starting_user_note_2: checkOrigin.starting_user_note_2,
          shipping_inventory_type: checkOrigin.shipping_inventory_type,
          shipping_id_list: checkOrigin.shipping_id_list,
        },
      });
      await tx.the_origins.update({
        where: {
          id: +id,
        },
        data: {
          ...bodyUpdate,
        },
      });
      await Promise.all(
        checkOrigin.ingredient.map((item) => {
          if (item.shipping_inventory_id) {
            return tx.inventories.updateMany({
              where: {
                id: item.shipping_inventory_id,
                is_history_cancel_locked: false,
              },
              data: {
                is_history_cancel_locked: true,
              },
            });
          }
        })
      );
    });

    return this.SUCCESS({ message: MESSAGE.MSG_FIXED_SHIPPING_INFO });
  }

  async exportData(params) {
    const { user, query } = params;
    const {
      startDate,
      endDate,
      licenseNumber,
      destination,
      sortBy,
      note1,
      note2,
      enterpriseName,
      code,
    } = query;

    const connect = this.DB.READ;

    const searchCondition = {
      delete_flag: false,
      starting_enterprise_id: user.enterprise_id,
      shipping_net_weight: { not: null },
      starting_user_id: user.staff_type === STAFF_TYPE_ENUM.STAFF ? user.id : undefined,
      destination_license_number: licenseNumber ? { contains: licenseNumber } : undefined,
      destination_user_note_1: note1 ? { contains: note1 } : undefined,
      destination_user_note_2: note2 ? { contains: note2 } : undefined,
      destination_user_name: destination ? { contains: destination } : undefined,
      destination_enterprise_name: enterpriseName ? { contains: enterpriseName } : undefined,
      destination_enterprise: code ? { OR: [{ enterprise_code: { contains: code } }] } : undefined,
      shipping_date: {
        gte: startDate ? dayjs.getDateFromJST(`${startDate} 00:00:00`).toDate() : undefined,
        lte: endDate ? dayjs.getDateFromJST(`${endDate} 23:59:59`).toDate() : undefined,
      },
    };

    const count = await connect.the_origins.count({ where: searchCondition });
    if (count > LIMIT_EXPORT) {
      throw new ControlledException(MESSAGE.MSG_LIMIT_EXPORT);
    }

    const data = await connect.the_origins.findMany({
      select: {
        shipping_date: true,
        code: true,
        shipping_net_weight: true,
        starting_user_name: true,
        starting_license_number: true,
        starting_enterprise_name: true,
        destination_user_name: true,
        destination_enterprise_name: true,
        shipping_type_diff: true,
        shipping_reason_diff: true,
        destination_user: {
          select: {
            role: true,
            enterprise_type: true,
            staff_type: true,
          },
        },
        starting_user: {
          select: {
            role: true,
            enterprise_type: true,
            staff_type: true,
          },
        },
        shipping_id_list: true,
      },
      where: searchCondition,
      orderBy:
        sortBy === 'id'
          ? { id: 'desc' }
          : sortBy === 'code'
            ? { destination_enterprise: { enterprise_name_kana_nospace: 'asc' } }
            : sortBy === 'shipping_net_weight'
              ? { shipping_net_weight: 'desc' }
              : {},
    });

    const dataWithInventory = await Promise.all(
      data.map(async (item) => {
        const lstId =
          item.shipping_id_list?.flatMap((val) => val.the_origins?.map((o) => o.id) || []) || [];

        let inventory_info_codes;
        if (lstId.length) {
          const inventoryList = await connect.the_origins.findMany({
            where: {
              id: { in: lstId },
              delete_flag: false,
              arrival_date: { not: null },
            },
            select: {
              id: true,
              code: true,
              destination_enterprise_name: true,
              destination_user_name: true,
            },
          });

          inventory_info_codes = inventoryList.map((inv) => ({
            code: inv.code,
            supplier_name: inv.destination_enterprise_name,
          }));
        }

        return {
          ...item,
          inventory_info_codes,
        };
      })
    );
    return this.SUCCESS({ data: dataWithInventory });
  }

  async #getShippingInfo(connect, id) {
    const data = await connect.the_origins.findFirst({
      where: {
        id: +id,
        delete_flag: false,
      },
      select: {
        id: true,
        shipping_id_list: true,
        code: true,
        qr_code: true,
        shipping_net_weight: true,
        shipping_date: true,
        arrival_date: true,
        destination_enterprise_name: true,
        starting_enterprise_name: true,
        destination_user_name: true,
        starting_user_name: true,
        arrival_net_weight: true,
        starting_user: {
          select: {
            enterprise_type: true,
            role: true,
            staff_type: true,
          },
        },
        destination_user: {
          select: {
            enterprise_type: true,
            role: true,
            staff_type: true,
          },
        },
      },
    });
    if (!data || !data.shipping_id_list.length) {
      return data;
    }
    const lstId = [];
    data.shipping_id_list.forEach((value) => {
      if (value.the_origins) {
        value.the_origins.forEach((o) => lstId.push(o.id));
      }
    });
    data.shippingInfo = await Promise.all(
      lstId.map((value) => this.#getShippingInfo(connect, value))
    );
    return data;
  }

  async exportShippingDetail(params) {
    const { user, query } = params;
    const { id } = query;
    const connect = this.DB.READ;
    let result;

    // Validate id parameter
    if (!id || isNaN(+id) || +id <= 0) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO, {}, 400);
    }

    const data = await connect.the_origins.findFirst({
      where: {
        id: +id,
        delete_flag: false,
        starting_enterprise_id: user.enterprise_id,
        starting_user_id:
          user.role === ROLES_ENUM.NORMAL_USER &&
            user.enterprise_type === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE &&
            user.staff_type === STAFF_TYPE_ENUM.STAFF
            ? user.id
            : undefined,
      },
      select: {
        id: true,
        destination_enterprise: {
          select: {
            type: true,
          },
        },
        shipping_id_list: true,
        code: true,
        qr_code: true,
        shipping_net_weight: true,
        shipping_date: true,
        arrival_date: true,
        destination_enterprise_name: true,
        starting_enterprise_name: true,
        destination_user_name: true,
        starting_user_name: true,
        starting_user: {
          select: {
            enterprise_type: true,
            role: true,
            staff_type: true,
          },
        },
        destination_user: {
          select: {
            enterprise_type: true,
            role: true,
            staff_type: true,
          },
        },
      },
    });

    if (!data) {
      result = null;
    }

    if (+data.destination_enterprise.type !== ENTERPRISE_TYPE_ENUM.FOREIGN) {
      result = data;
    }

    result = await this.#getShippingInfo(connect, +id);

    if (!result) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO);
    }

    result.foreign_flag = +data.destination_enterprise.type === ENTERPRISE_TYPE_ENUM.FOREIGN;
    return this.SUCCESS(result);
  }

  async getExportToday(params) {
    const { user } = params;
    const connect = this.DB.READ;
    const statisticsDateFrom = await connect.users.findFirst({
      where: {
        id: user.id,
      },
      select: {
        statistics_date_from: true,
        created_on: true,
        enterprise_type: true,
        staff_type: true,
      },
    });
    const dateCompare = statisticsDateFrom.statistics_date_from
      ? dayjs(statisticsDateFrom.statistics_date_from)
      : dayjs(statisticsDateFrom.created_on);
    const checkDiff = dayjs().diff(dateCompare.format('YYYY-MM-DD'), 'day');
    const filter = {
      select: {
        destination_enterprise: true,
        user_create: true,
        starting_user_id: true,
      },
      where: {
        NOT: {
          shipping_net_weight: null,
        },
        AND: {
          delete_flag: false,
          destination_enterprise_id: user.enterprise_id,
          shipping_type: 1,
          shipping_date: {
            lte: dayjs.getDate().toDate(),
            gte:
              checkDiff === 0 ? dateCompare : dayjs.getDate().hour(0).minute(0).second(0).toDate(),
          },
          arrival_net_weight: {
            gt: 0,
          },
        },
      },
    };
    const isNotStaff = user.user_code?.endsWith('-0000');
    if (!isNotStaff) {
      filter.where.AND.destination_user_id = user.id;
    }
    const data = await connect.the_origins.findMany({
      ...filter,
    });
    const result = {
      persons: [],
      weight: 0,
    };
    data.reduce((init, item) => {
      init.persons.push(item.starting_user_id);
      init.weight += +item.shipping_net_weight;
      return init;
    }, result);
    return this.SUCCESS({
      weight: result.weight,
      persons: new Set([...result.persons]).size,
      date: dateCompare.format('YYYY/MM/DD'),
    });
  }

  async resetExportToday(params) {
    const { user } = params;
    const connect = this.DB.WRITE;
    await connect.users.update({
      where: {
        id: user.id,
      },
      data: {
        statistics_date_from: dayjs().toDate(),
      },
    });
    return this.SUCCESS();
  }
}

module.exports = ShippingService;
