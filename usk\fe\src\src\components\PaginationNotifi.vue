<template>
  <q-pagination v-if="provideData.totalPage.value > 1" v-model="provideData.pageIndex.value"
    :max="provideData.totalPage.value" :max-pages="5" boundary-links :boundary-numbers="false"
    icon-first="keyboard_double_arrow_left" icon-last="keyboard_double_arrow_right" outline padding="0.625rem"
    color="blue-3" active-design="unelevated" active-color="blue-3" active-text-color="white"
    input-class="tw:text-s-design" @update:model-value="provideData.onChangedPageIndex"></q-pagination>
</template>
<script setup>
import { inject } from 'vue';

const provideData = inject('paginationItemProvideData');
</script>

<style>
@media (min-width: 1440px) {
  .q-pagination .q-pagination__content button {
    font-size: 34px !important;
    line-height: 1.5 !important;
    width: 2.5rem !important;
    height: 2.5rem !important;
    min-height: 2.5rem !important;
    min-width: 2.5rem !important;
  }
}

@media (min-width: 1200px) {
  .q-pagination .q-pagination__content button {
    font-size: 34px !important;
    line-height: 1.2 !important;
    width: 2.5rem !important;
    height: 2.5rem !important;
    min-height: 2.5rem !important;
    min-width: 2.5rem !important;
  }
}
@media (max-width: 1199px) {
  .q-pagination .q-pagination__content button {
    font-size: 16px !important;
    line-height: 1.2 !important;
    width: 2.5rem !important;
    height: 2.5rem !important;
    min-height: 2.5rem !important;
    min-width: 2.5rem !important;
  }
}

/* .q-pagination__middle {
  height: 4.25rem !important;
} */

.q-pagination__middle .q-btn {
  height: 4.25rem !important;
  min-width: 4.25rem !important;
  margin-top: 1px !important;
}

.q-pagination .q-pagination__content button {
  width: 4.25rem !important;
  height: 4.25rem !important;
  min-height: 4.25rem !important;
  min-width: 4.25rem !important;
  padding: 0 !important;
}
</style>
