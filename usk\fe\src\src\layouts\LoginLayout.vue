<template>
  <q-layout view="lHh Lpr lFf" class="tw:flex">
    <q-header elevated :class="`tw:bg-white`">
      <q-toolbar>
        <q-btn
          flat
          dense
          round
          icon="menu"
          aria-label="Menu"
          class="tw:text-[#333333]"
          @click.prevent="toggleLeftDrawer"
        />

        <q-toolbar-title>
          <div
            class="tw:flex tw:text-[#333333] tw:font-bold tw:text-[15px]
            tw:leading-[1.5] tw:tl:text-[28px] tw:dt:text-[32px] tw:flex-wrap tw:tl:flex-row tw:flex-col tw:tl:py-2"
          >
            <div class="item-header">{{ envPrefix }}シラスウナギトレーサビリティ支援システム</div>
          </div>
        </q-toolbar-title>
      </q-toolbar>
    </q-header>

    <q-drawer v-model="leftDrawerOpen" show-if-above bordered behavior="mobile">
      <q-list class="tw:flex tw:flex-col tw:h-full">
        <q-item-label header>
          <img
            :src="CloseSvg"
            alt=""
            class="tw:w-[2.5rem] tw:h-[2.5rem] tw:hover:cursor-pointer"
            @click.prevent="toggleLeftDrawer"
          />
        </q-item-label>

        <EssentialLink />
        <div class="tw:px-4 tw:pb-2 tw:mt-10">
          <span class="tw:text-s-design">お問い合わせ：</span>
          <div>
            <span class="tw:text-xxs-design">一般社団法人</span>
            <span class="tw:text-xs-design tw:font-bold tw:ml-3"
              >全日本持続的養鰻機構</span
            >
          </div>
          <div>
            <span class="tw:text-[1.875rem]">Tel.</span>
            <a
              href="tel:03-5797-7690"
              class="tw:text-blue-3 hover:tw:opacity-80 tw:text-[1.875rem] tw:tl:hidden"
            >
              03-5797-7690
            </a>
            <span class="tw:text-[1.875rem] tw:hidden tw:tl:inline">
              03-5797-7690</span
            >
          </div>
        </div>
      </q-list>
    </q-drawer>

    <div class="tw:w-screen tw:flex-1">
      <q-scroll-area
        :thumb-style="thumbStyle"
        :bar-style="barStyle"
        class="tw:w-full tw:h-full tw:bg-[#f7f7f9]"
        ref="scrollAreaRef"
      >
        <q-page-container class="tw:px-3 tw:tl:px-8 tw:bg-[#F7F7F9]">
          <div class="w-content tw:tl:w-[calc(100vw-4rem)] tw:dt:mt-[1rem]">
            <div
              class="tw:pt-2 tw:inline-flex tw:items-center tw:hover:cursor-pointer tw:hover:opacity-70"
              @click.prevent="backRoute"
              v-if="
                router.currentRoute.value?.meta?.routeBack &&
                !router.currentRoute.value?.meta?.isBreadcrumb
              "
            >
              <q-icon
                name="arrow_back"
                class="tw:tl:text-[2rem] tw:text-lg tw:text-gray-1"
              />
              <span
                class="tw:ml-2 tw:tl:text-[2rem] tw:font-black tw:text-lg tw:text-gray-1"
              >
                戻る</span
              >
            </div>
            <div
              class="tw:flex tw:justify-between"
              v-if="
                router.currentRoute.value?.meta?.title &&
                !router.currentRoute.value?.meta?.isBreadcrumb
              "
            >
              <div
                class="tw:tl:leading-[4rem] tw:tl:text-[4rem] tw:font-bold tw:text-2xl tw:border-b tw:border-gray tw:pb-6 tw:pt-4 tw:flex-1"
              >
                {{ router.currentRoute.value.meta.title }}
              </div>
            </div>
            <div
              class="tw:flex tw:justify-between"
              v-if="router.currentRoute.value?.meta?.isBreadcrumb"
            >
              <!-- Breadcrumbs -->
              <q-breadcrumbs
                class="tw:text-xs-design tw:font-bold tw:text-blue-3 tw:border-gray tw:flex-1"
                active-color="tw:text-blue-3"
              >
                <template v-slot:separator>
                  <q-icon
                    size="1.5em"
                    name="chevron_right"
                    class="tw:text-[#7E8093]"
                  />
                </template>
                <q-breadcrumbs-el
                  class="tw:cursor-pointer"
                  label="トップ"
                  @click.prevent="gotoPage('home')"
                />
                <q-breadcrumbs-el
                  :label="`${router.currentRoute.value.meta.title}`"
                  @click.prevent="
                    gotoPage(`${router.currentRoute.value.meta.prevRoute}`)
                  "
                />
                <div
                  v-if="router.currentRoute.value.meta.tag"
                  class="tw:ml-2 tw:text-[#333333] tw:flex tw:items-center tw:justify-center"
                >
                  <span class="tw:text-[#7E8093] tw:pb-1 tw:pl-4 tw:pr-5"
                    >|</span
                  >
                  {{
                    router.currentRoute.value.meta.tag
                      ? router.currentRoute.value.meta.tag
                      : ""
                  }}
                </div>
              </q-breadcrumbs>
            </div>
            <router-view />
          </div>
        </q-page-container>
      </q-scroll-area>
    </div>
  </q-layout>
</template>

<script setup>
import CloseSvg from 'assets/CloseSvg.svg';
import DescriptionSvg from 'assets/DescriptionSvg.svg';
import GradingSvg from 'assets/GradingSvg.svg';
import LicenseSvg from 'assets/LicenseSvg.svg';
import PolicySvg from 'assets/PolicySvg.svg';
import EssentialLink from 'components/EssentialLink.vue';
import { onMounted, provide, ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const envPrefix = ref('');

const linksList = ref([
  {
    title: 'プライバシーポリシー',
    // vs14 done
    link: 'privacyPolicyVS',
    svg: PolicySvg,
    role: [],
  },
  {
    title: 'マニュアル',
    // vs14 done
    external: true,
    link: `${window.location.origin}/document/usersite/manual.pdf`,
    svg: DescriptionSvg,
    role: [],
  },
  {
    title: 'ライセンス',
    // vs14 done
    link: 'licenseVS',
    svg: LicenseSvg,
    role: [],
  },
  {
    title: '利用規約',
    // vs14 done
    link: 'termsOfUserVS',
    svg: GradingSvg,
    role: [],
  },
]);

const leftDrawerOpen = ref(false);

provide('items', linksList);

const toggleLeftDrawer = () => {
  leftDrawerOpen.value = !leftDrawerOpen.value;
};

const backRoute = async () => {
  if (router.currentRoute.value.meta.routeBack) {
    router.back();
  }
};

const gotoPage = async name => {
  await router.push({ name });
};

onMounted(() => {
  envPrefix.value = `${process.env.PREFIX}`;
});

// style scroll
const thumbStyle = {
  right: '4px',
  borderRadius: '5px',
  backgroundColor: '#737373',
  width: '2px',
  opacity: 0.75,
};
const barStyle = {
  right: '2px',
  borderRadius: '9px',
  backgroundColor: '#737373',
  width: '4px',
  opacity: 0.2,
};
</script>

<style scoped>
:deep(aside.q-drawer) {
  width: 312px !important;

  @media (min-width: 960px) {
    width: 406px !important;
  }
}

:deep(.q-item__label) {
  line-height: initial !important;
}

:deep(.item-header) {
  @media (min-width: 960px) and (max-width: 1199px) {
    font-size: 16px !important;
  }
}

:deep(button[aria-label="Menu"] .q-icon) {
  font-size: 2.25rem !important;
}
</style>
