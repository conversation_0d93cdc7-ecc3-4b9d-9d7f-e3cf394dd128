<template>
  <q-card
    class="tw:p-5 tw:flex tw:flex-col tw:h-full tw:pb-[15rem] tw:tl:pb-[8rem]"
  >
    <div class="tw:text-l-design tw:font-bold">取引先新規登録</div>
    <div class="tw:text-m-design tw:mt-5">
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-[#D2D2D2]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 事業者区分 </span>
          <q-badge
            class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2"
          >
            必須
          </q-badge>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#D2D2D2]"
        >
          <span>{{ mapEnterpriseTypeToDisplay(newPartnerDetail.enterpriseType) }}</span>
        </div>
      </div>
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#D2D2D2]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 都道府県 </span>
          <q-badge
            class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2"
          >
            必須
          </q-badge>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#D2D2D2]"
        >
          <span>{{ newPartnerDetail.provinceName }}</span>
        </div>
      </div>
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#D2D2D2]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 届出番号 </span>
          <q-badge
            class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2"
          >
            必須
          </q-badge>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:min-h-[4.25rem]
          tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#D2D2D2]"
        >
          <span>{{ newPartnerDetail.enterpriseCode }}</span>
        </div>
      </div>
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#D2D2D2]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 事業者名 </span>
          <q-badge
            class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2"
          >
            必須
          </q-badge>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#D2D2D2]"
        >
          <span>{{ newPartnerDetail.enterpriseName }}</span>
        </div>
      </div>
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#D2D2D2]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 事業者名（カナ）</span>
        </div>
        <div
          class="tw:tl:w-[70%] tw:min-h-[4.25rem] tw:bg-white
          tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#D2D2D2]"
        >
          <span>{{ newPartnerDetail.enterpriseNameKana }}</span>
        </div>
      </div>
      <div
        class="ttw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#D2D2D2]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 取引先区分 </span>
          <q-badge
            class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2"
          >
            必須
          </q-badge>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#D2D2D2]"
        >
          <span>{{
            mapPartnerTypeToDisplay(newPartnerDetail.partnerType || [])
          }}</span>
        </div>
      </div>
    </div>

    <q-footer
      elevated
      class="tw:bg-white tw:p-4 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
      tw:tl:justify-between tw:flex tw:justify-center tw:mt-4 tw:flex-col tw:gap-4 tw:tl:flex-row"
    >
      <BaseButton
        outline

        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
        tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]`"
        label="入力内容を修正する"
        @click.prevent="router.back()"
      />
      <BaseButton
        outline

        class="tw:rounded-[40px]"
        :class="`tw:bg-blue-3 tw:text-white tw:text-m-design tw:tl:font-bold
        tw:tl:w-[24.5rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
        tw:w-full`"
        label="登録する"
        @click.prevent="handleClickConfirm"
      />
    </q-footer>
  </q-card>
</template>
<script setup>
import { PARTNER_TYPE_ENUM } from 'helpers/constants';
import { storeToRefs } from 'pinia';
import BaseButton from 'src/components/base/vs/BaseButton.vue';
import partnerService from 'services/locatedTransaction.service';
import { ENTERPRISE_TYPE_ENUM } from 'src/helpers/constants';
import { useAppStore } from 'src/stores/app-store';
import { useConfirmFormStore } from 'stores/confirm-form-store';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import toast from 'src/shared/utilities/toast';

// #region variable
const router = useRouter();
const { getConfirmData } = useConfirmFormStore();
const newPartnerDetail = ref({});
const { previousRoute } = storeToRefs(useAppStore());

const partnerTypeOptions = [
  {
    label: '仕入先',
    value: PARTNER_TYPE_ENUM.SUPPLIER.toString(),
  },
  {
    label: '出荷先',
    value: PARTNER_TYPE_ENUM.SHIPPER.toString(),
  },
];

const enterpriseTypeOptions = [
  {
    label: '採捕事業者',
    value: ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE.toString(),
  },
  {
    label: '取扱事業者',
    value: ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE.toString(),
  },
];
// #endregion variable

// #region function
const handleClickConfirm = async () => {
  const { provinceName, ...rest } = newPartnerDetail.value;
  const payload = {
    ...rest,
    partnerType: newPartnerDetail.value.partnerType.map(Number),
    enterpriseType: parseInt(newPartnerDetail.value.enterpriseType),
    enterpriseNameKana: newPartnerDetail.value.enterpriseNameKana || undefined,
  };

  const response = await partnerService.registerHandMade(payload);
  if (response.code === 0) {
    toast.access(response.payload.message);
    router.push({
      name: 'partner',
    });
  }
};
// #endregion function

// #region helper function
const mapEnterpriseTypeToDisplay = enterpriseType =>
  enterpriseTypeOptions.find(option => option.value === enterpriseType)
    ?.label || '';
const mapPartnerTypeToDisplay = partnerType =>
  partnerType
    .sort()
    .map(
      type =>
        partnerTypeOptions.find(option => option.value === type)?.label || ''
    )
    .join('/');
// #endregion helper function

onMounted(async () => {
  const newPartnerStoreData = getConfirmData();
  if (newPartnerStoreData && previousRoute.value.name === 'partnerInput') {
    newPartnerDetail.value = newPartnerStoreData;
  } else {
    router.push({
      name: 'partnerInput',
    });
  }
});
</script>
