import { wdbConnect } from './base/knex.mjs';
import { ApplicationException } from './base/errors.mjs';
import dayjs from './base/dayjs.mjs';

const processFunc = async (wdb) => {
  const isolationLevel = 'read committed';
  const trx = await wdb.transaction({ isolationLevel });
  try {
    console.log(process.env)
    const deleteDraftDays = process.env.DAFT || '7';
    const deleteCompletedDays = process.env.COMPLETE || '30';

    console.log(`Configuration: DRAFT retention = ${deleteDraftDays} days, COMPLETED retention = ${deleteCompletedDays} days`);

    const batchTime = dayjs.getDateFromJST().format('YYYY-MM-DD 23:59:59');
    console.log(`Batch execution time: ${batchTime}`);

    // Đếm số bản ghi sẽ bị xóa trước khi thực hiện
    const draftCount = await trx('code_suffixes_alloc')
      .where('alloc_status', 1)
      .andWhereRaw(`'${batchTime}' > created_on + interval '${deleteDraftDays} day'`)
      .count('* as count')
      .first();

    const completedCount = await trx('code_suffixes_alloc')
      .where('alloc_status', 2)
      .andWhereRaw(`'${batchTime}' > created_on + interval '${deleteCompletedDays} day'`)
      .count('* as count')
      .first();

    console.log(`Records to be deleted: DRAFT = ${draftCount.count}, COMPLETED = ${completedCount.count}`);

    // Thực hiện xóa
    const deletedRows = await trx('code_suffixes_alloc')
      .where(function () {
        this.where('alloc_status', 1)
          .andWhereRaw(` '${batchTime}' > created_on + interval '${deleteDraftDays} day'`);
      })
      .orWhere(function () {
        this.where('alloc_status', 2)
          .andWhereRaw(`'${batchTime}' > created_on + interval '${deleteCompletedDays} day'`);
      })
      .del();

    console.log(`Successfully deleted ${deletedRows} records from code_suffixes_alloc table`);

    await trx.commit({ isolationLevel });
  } catch (ex) {
    console.error('Error during transaction, rolling back:', ex.message);
    await trx.rollback();
    throw ex;
  }
}

export const handler = async (event, context) => {
  console.log('Lambda function started');
  console.log('Event:', JSON.stringify(event, null, 2));
  console.log('Context:', JSON.stringify(context, null, 2));

  const wdb = wdbConnect();
  try {
    await processFunc(wdb);
    console.log('Batch deletion process completed successfully');
  } catch (ex) {
    if (!(ex instanceof ApplicationException)) {
      console.error('Error during batch deletion:', ex.stack);
    }
    throw ex; // Re-throw to ensure Lambda reports the failure
  }
  finally {
    wdb.destroy();
  }
};
