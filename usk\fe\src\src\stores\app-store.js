import { CHECK_ROLE } from 'helpers/common';
import { ROLES_ENUM } from 'helpers/constants';
import { defineStore } from 'pinia';
import { FORMAT_DATE } from 'src/helpers/common';
import { ENTERPRISE_TYPE_ENUM } from 'src/helpers/constants';

export const useAppStore = defineStore('appStore', {
  state: () => ({
    loading: false,
    isLoadingManual: false,
    role: '',
    previousRoute: {},
    colorMain: 'blue-1',
    colorSub: 'blue-3',
    qrCode: '',
    detailShipment: {},
    settingUser: null,
    hasUnreadNotificationFlag: false,
    mesErr: '',
    isNonActive: '',
    dataAdminRegisterUser: {
      enterpriseType: '',
      enterpriseCode: '',
      enterpriseName: '',
      enterpriseNameKana: '',
      password: '',
      typeStaffOrEnterprise: 0,
      provinceId: '',
      phone: '',
      licenseNumber: '',
      startExpiryDate: FORMAT_DATE(Date.now()),
      endExpiryDate: FORMAT_DATE(Date.now()),
      status: 0,
      note1: '',
      note2: '',
    },
    dataAdminEditUser: {},
    previousQuery: null,
  }),
  getters: {
    isLoading: state => state.loading,
  },
  actions: {
    setPreviousRoute(route) {
      this.previousRoute = route;
    },
    setLoading(boolFlg) {
      this.loading = boolFlg;
    },
    setLoadingManual(boolFlg) {
      this.isLoadingManual = boolFlg;
    },
    async setRole(user) {
      this.role = user.role;
      this.setColorMain(user);
      this.setColorSub(user);
    },
    setColorMain(user) {
      if (
        CHECK_ROLE(
          [ROLES_ENUM.ADMIN, ROLES_ENUM.NORMAL_STAFF],
          [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE],
          [],
          user
        )
      ) {
        this.colorMain = 'blue-1';
      } else if (
        CHECK_ROLE(
          [ROLES_ENUM.ADMIN, ROLES_ENUM.NORMAL_STAFF],
          [ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
          [],
          user
        )
      ) {
        this.colorMain = 'green-1';
      } else if (
        CHECK_ROLE(
          [ROLES_ENUM.NORMAL_STAFF],
          [ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
          [],
          user
        )
      ) {
        this.colorMain = 'purple-1';
      } else {
        this.colorMain = 'blue-1';
      }
    },
    setColorSub(user) {
      if (
        CHECK_ROLE(
          [ROLES_ENUM.ADMIN, ROLES_ENUM.NORMAL_STAFF],
          [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE],
          [],
          user
        )
      ) {
        this.colorSub = 'blue-3';
      } else if (
        CHECK_ROLE(
          [ROLES_ENUM.ADMIN, ROLES_ENUM.NORMAL_STAFF],
          [ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
          [],
          user
        )
      ) {
        this.colorSub = 'green-2';
      } else if (
        CHECK_ROLE(
          [ROLES_ENUM.NORMAL_STAFF],
          [ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
          [],
          user
        )
      ) {
        this.colorSub = 'purple-2';
      } else {
        this.colorSub = 'blue-3';
      }
    },
    setQrCode(qrCode) {
      this.qrCode = qrCode;
    },
    setDetailShipment(data) {
      this.detailShipment = data;
    },
    setSettingUser(data) {
      this.settingUser = data;
    },
    setHasUnreadNotificationFlag(flag) {
      this.hasUnreadNotificationFlag = flag;
    },
    setMesErr(mes) {
      this.mesErr = mes;
    },
    setIsNonActive(value) {
      this.isNonActive = value;
    },
    setAdminRegisterUser(value) {
      this.dataAdminRegisterUser = value;
    },
    setAdminEditUser(value) {
      this.dataAdminEditUser = value;
    },
    setPreviousQuery(query) {
      this.previousQuery = query;
    },
    clearPreviousQuery() {
      this.previousQuery = null;
    },
  },
});
