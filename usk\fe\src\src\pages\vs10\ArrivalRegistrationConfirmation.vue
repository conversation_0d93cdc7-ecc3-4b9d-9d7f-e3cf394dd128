<template>
  <div class="tw:h-full tw:flex tw:flex-col">
    <!-- #endregion -->
    <q-card class="tw:flex-1 tw:p-5 tw:flex tw:flex-col tw:pb-[18rem] tw:tl:pb-[7rem]">
      <h2 :class="`tw:text-xs-design tw:tl:font-bold tw:mb-4`">
        入力内容を確認して「登録する」ボタンを押してください。
      </h2>
      <div
        class="tw:text-[#333333] tw:grid tw:grid-cols-1 tw:gap-y-5 tw:tl:grid-cols-2 tw:gap-x-12 tw:tl:gap-y-2"
      >
        <!-- 漁獲/荷口番号 -->
        <div>
          <BaseLabel label="漁獲/荷口番号" />
          <div>
            <span class="tw:text-xl-design">
              {{ maskCodeString(displayData.code) }}
            </span>
          </div>
        </div>
        <!-- 仕入先 -->
        <div>
          <BaseLabel label="仕入先" />
          <div>
            <span class="tw:text-l-design">
              {{ displayData.supplierInfo?.name || '' }}
            </span>
          </div>
        </div>
        <!-- 入荷日 -->
        <div>
          <BaseLabel label="入荷日" isRequired />
          <div class="tw:font-bold">
            <span class="tw:text-xl-design tw:gap-2 tw:items-center">
              {{ FORMAT_DATE(displayData.date) }}
            </span>
          </div>
        </div>
        <!-- element offset -->
        <div></div>
        <!-- 出荷量 -->
        <div v-if="displayData.displayShipmentWeight">
          <BaseLabel label="出荷量" />
          <div>
            <span class="tw:text-xl-design tw:mr-1"> {{ displayData.shipping_net_weight }}</span>
            <span class="tw:text-[2.375rem]"> g</span>
          </div>
        </div>
        <!-- element offset -->
        <div v-if="displayData.displayShipmentWeight"></div>
        <!-- 入荷量 -->
        <div>
          <BaseLabel label="入荷量" isRequired />
          <div class="tw:font-bold">
            <span class="tw:text-xl-design tw:mr-1"> {{ displayData.netWeight }}</span>
            <span class="tw:text-[2.375rem]"> g</span>
          </div>
        </div>
        <div></div>

      </div>
        <!-- element offset -->
      <div v-if="displayData.typeDiff">
          <BaseLabel :class="`tw:text-xs-design`" label="差異の理由" isRequired />
          <div
            class="tw:text-xs-design"
          >
            <span>
              {{ reasonOptions.find((item) => item.value === displayData.typeDiff)?.label }}
            </span>
            <span
              v-if="displayData.typeDiff === TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER"
              v-html="clearHTML(linkify(`（${displayData.reasonDiff}）`))"
            >
            </span>
          </div>
        </div>
    </q-card>
  </div>
  <q-footer
    elevated
    class="tw:bg-white tw:p-4 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
    tw:tl:justify-between tw:flex tw:justify-center tw:mt-4 tw:flex-col tw:gap-4 tw:tl:flex-row"
  >
    <BaseButton
      outline
      padding="1.25rem"
      class="tw:rounded-[40px] tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:max-h-[5.43rem] tw:tl:min-h-[5.43rem] tw:h-[5.43rem]"
      label="入力内容を修正する"
      @click="handleBack"
    />
    <BaseButton
      outline
      padding="1.25rem"
      class="tw:rounded-[40px] tw:bg-[#004AB9] tw:text-white tw:text-m-design
      tw:tl:w-[24.5rem] tw:tl:max-h-[5.43rem] tw:tl:min-h-[5.43rem] tw:h-[5.43rem]"
      label="登録する"
      @click="handleClickSubmit"
    />
  </q-footer>
</template>
<script setup>
import BaseButton from 'components/base/vs/BaseButton.vue';
import { storeToRefs } from 'pinia';
import BaseLabel from 'src/components/base/vs/BaseLabel.vue';
import {
  clearHTML,
  doParseFloatNumber,
  FORMAT_DATE,
  linkify,
  maskCodeString,
} from 'src/helpers/common';
import {
  SHOW_DEFAULT_SCAN_QR_ENUM,
  TYPE_DIFFERENCE_WEIGHT_ENUM,
} from 'src/helpers/constants';
import MESSAGE from 'src/helpers/message';
import arrivalService from 'src/shared/services/arrival.service';
import toast from 'src/shared/utilities/toast';
import { useAppStore } from 'src/stores/app-store';
import { useConfirmFormStore } from 'src/stores/confirm-form-store';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

const { previousRoute, settingUser } = storeToRefs(useAppStore());
const displayData = ref({});
const { getConfirmData } = useConfirmFormStore();
const router = useRouter();
const reasonOptions = ref([
  { label: '斃死', value: TYPE_DIFFERENCE_WEIGHT_ENUM.DEATH },
  { label: '計量誤差', value: TYPE_DIFFERENCE_WEIGHT_ENUM.WEIGHT_ERROR },
  { label: 'その他', value: TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER },
]);

// #region functions
const handleBack = () => {
  router.back();
};

const handleClickSubmit = async () => {
  // build payload for registering outbound shipment
  const payload = {
    code: displayData.value.code,
    date: displayData.value.date,
    gross_weight: doParseFloatNumber(displayData.value.grossWeight),
    tare_weight: doParseFloatNumber(displayData.value.tareWeight) || 0,
    quantity: doParseFloatNumber(displayData.value.quantity),
    supplier: displayData.value.supplier,
    type_diff: displayData.value.typeDiff || undefined,
    reason_diff: displayData.value.reasonDiff || undefined,
  };

  const result = await arrivalService.registerArrivalQr(payload);
  if (result.code === 402) {
    router.push('home');
    return;
  }

  // redirect to the appropriate page based on the user's settings
  toast.access(MESSAGE.MSG_RESISTER_ARRIVAL_INFO);
  switch (settingUser.value.qr_scan_init) {
    case SHOW_DEFAULT_SCAN_QR_ENUM.USE_CAMERA:
      router.push({ name: 'arrivalQrCamera' });
      break;
    default:
      router.push({ name: 'arrivalQrScan' });
      break;
  }
};
// #endregion

// #region helpers functions

// #endregion

onMounted(() => {
  const confirmData = getConfirmData();
  if (confirmData && previousRoute.value.name === 'arrivalRegistration') {
    displayData.value = confirmData;
  } else {
    router.push({ name: 'arrivalRegistration' });
  }
});
</script>
<style scoped></style>
