const dayjs = require('../boot/dayjs');
const BaseService = require('../base/serviceFn');
const { PAGINATION } = require('../helpers/enum');
const { MESSAGE } = require('../utils/message');
const { ControlledException } = require('../base/errors');
class InventoryManagementService extends BaseService {
  // ====== 1. Private method ==========
  // check group name is existed
  async #checkGroupNameExisted(user, groupName) {
    const connect = this.DB.READ;

    const group = await connect.inventories.findFirst({
      where: {
        user_id: user.id,
        group_name: groupName,
        delete_flag: false,
      },
    });

    return !!group;
  }

  // ====== 2. Public method ==========
  /**
   * get total weight inventory
   * @param {object} user - user information
   * @returns {Promise<object>} - response object { inventory }
   */
  async getTotalWeightInventory(user) {
    const connect = this.DB.READ;

    // get total weight inventory
    // get total net weight of all inventories
    const totalNetWeight = await connect.inventories.aggregate({
      _sum: {
        net_weight_inventory: true,
      },
      where: {
        user_id: user.id,
        net_weight_inventory: {
          gt: 0,
        },
        delete_flag: false,
      },
    });

    return this.SUCCESS({
      total_weight_inventory: totalNetWeight._sum.net_weight_inventory,
    });
  }

  /**
   * Get inventory list
   * @param {object} user - user information
   * @param {object} queries - queries for searching inventory
   * @returns {Promise<object>} - response object { total_item, items, total_net_weight }
   */
  async getInventoryList(user, queries) {

    const connect = this.DB.READ;

    // get queries
    const {
      sortBy,
      descending = PAGINATION.DEFAULT_DESCENDING,
      page = PAGINATION.FIRST_PAGE,
      limit = PAGINATION.PAGE_SIZE,
      enterpriseName,
      name,
      startDate,
      endDate,
      code,
      licenseNumber,
    } = queries;

    // if code is provided, get enterprise info first
    let enterpriseInfo = null;
    if (code) {
      enterpriseInfo = await connect.enterprises.findFirst({
        where: {
          enterprise_code: code,
          delete_flag: false,
        },
        select: {
          id: true,
        },
      });
    }
    // build origins search conditions
    const originsConditions = [];

    // search by enterprise name from starting_enterprise_name
    if (enterpriseName) {
      originsConditions.push({
        starting_enterprise_name: {
          contains: enterpriseName,
        },
      });
    }

    // search by user name from starting_user_name
    if (name) {
      originsConditions.push({
        starting_user_name: {
          contains: name,
        },
      });
    }

    // search by license number from start_license_name
    if (licenseNumber) {
      originsConditions.push({
        starting_license_number: {
          contains: licenseNumber,
        },
      });
    }

    // search by enterprise code - use enterprise info to search in origins
    if (code && enterpriseInfo) {
      originsConditions.push({
        starting_enterprise_id: enterpriseInfo.id,
      });
    }

    // build search condition
    const searchCondition = {
      user_id: user.id,
      net_weight_inventory: {
        gt: 0,
      },
      delete_flag: false,
      // combine all origins search conditions
      ...(originsConditions.length > 0 && {
        the_origins: {
          some: {
            AND: originsConditions,
          },
        },
      }),
      latest_arrival_date: {
        gte: startDate
          ? dayjs.getDateFromJST(`${startDate} 00:00:00`).toDate()
          : undefined,
        lte: endDate
          ? dayjs.getDateFromJST(`${endDate} 23:59:59`).toDate()
          : undefined,
      },
    };

    // calculate total item
    const total_item = await connect.inventories.count({
      where: searchCondition,
    });

    // if total item is 0, return empty items
    if (!total_item) {
      return this.SUCCESS({
        total_item,
        items: [],
        page: 1,
        page_size: +limit,
      });
    }

    // calculate offset
    const offset = (+page - 1) * +limit;
    let tempPage = +page;

    // if offset is greater than total item, recalculate page and offset
    if (offset >= total_item) {
      tempPage = 1;
    }

    // get items
    const items = await connect.inventories.findMany({
      select: {
        id: true,
        group_name: true,
        net_weight_inventory: true,
        net_weight_total: true,
        latest_arrival_date: true,
        // get the_origins to calc fallback group name
        the_origins: {
          select: {
            arrival_date: true,
            starting_enterprise: {
              select: {
                enterprise_name: true,
              },
            },
          },
          where: {
            delete_flag: false,
            arrival_date: {
              not: null,
            },
          },
        },
      },
      where: searchCondition,
      orderBy: {
        [sortBy]: descending === 'true' ? 'desc' : 'asc',
      },
    });

    // get total net weight of all inventories
    const totalNetWeight = await connect.inventories.aggregate({
      _sum: {
        net_weight_inventory: true,
      },
      where: {
        user_id: user.id,
        net_weight_inventory: {
          gt: 0,
        },
        delete_flag: false,
      },
    });

    const itemsWithFallbackGroupName = items.map((item) => {
      // get fallback group name
      if (!item.group_name) {
        // get first origin of inventory
        // because the inventory doesn't have group name, so it must have only one origin
        const origin = item.the_origins[0];

        // calc fallback group name
        const fallbackGroupName = `${dayjs
          .getDateFromJST(origin.arrival_date)
          .format('MM/DD')} ${origin.starting_enterprise.enterprise_name}`;
        delete item.the_origins;
        return {
          ...item,
          fallback_group_name: fallbackGroupName,
        };
      }
      // if inventory has group name, return it
      delete item.the_origins;
      return item;
    });

    return this.SUCCESS({
      total_item,
      items: itemsWithFallbackGroupName,
      total_net_weight: totalNetWeight._sum.net_weight_inventory,
      page: tempPage,
      page_size: +limit,
    });
  }

  /**
   * Get inventory has been edited list
   * @param {object} user - user information
   * @param {object} queries - queries for searching inventory
   * @returns {Promise<object>} - response object { total_item, items }
   */
  async getInventoryEditedList(user, queries) {
    const connect = this.DB.READ;

    // get queries
    const {
      sortBy,
      descending = PAGINATION.DEFAULT_DESCENDING,
      page = PAGINATION.FIRST_PAGE,
      limit = PAGINATION.PAGE_SIZE,
      group,
      supplier,
      startDate,
      endDate,
    } = queries;

    // build search condition
    const searchCondition = {
      user_id: user.id,
      delete_flag: false,
      is_display_inventories_history: true,
      inventory: {
        // search by supplier (starting_user_name)
        the_origins: supplier
          ? {
            some: {
              starting_user: {
                OR: [
                  {
                    name_nospace: {
                      contains: supplier.replace(/\s+/g, ''),
                    },
                  },
                  {
                    name_kana_nospace: {
                      contains: supplier.replace(/\s+/g, ''),
                    },
                  },
                ],
              },
            },
          }
          : undefined,
        group_name: group || undefined,
      },
      created_on: {
        gte: startDate
          ? dayjs.getDateFromJST(`${startDate} 00:00:00`).toDate()
          : undefined,
        lte: endDate
          ? dayjs.getDateFromJST(`${endDate} 23:59:59`).toDate()
          : undefined,
      },
    };

    // calculate total item
    const total_item = await connect.inventories_history.count({
      where: searchCondition,
    });

    // if total item is 0, return empty items
    if (!total_item) {
      return this.SUCCESS({
        total_item,
        items: [],
        page: 1,
        page_size: +limit,
      });
    }

    // calculate offset
    const offset = (+page - 1) * +limit;
    let tempPage = +page;

    if (offset >= total_item) {
      tempPage = Math.ceil(total_item / +limit);
    }

    // get items
    const items = await connect.inventories_history.findMany({
      select: {
        id: true,
        group_name: true,
        net_weight_inventory: true,
        new_net_weight_inventory: true,
        reason_diff: true,
        type_diff: true,
        created_on: true,
        inventory: {
          select: {
            group_name: true,
            the_origins: {
              select: {
                arrival_date: true,
                starting_enterprise: {
                  select: {
                    enterprise_name: true,
                  },
                },
              },
              where: {
                delete_flag: false,
                arrival_date: {
                  not: null,
                },
              },
            },
          },
        },
      },
      where: searchCondition,
      orderBy: {
        [sortBy]: descending === 'true' ? 'desc' : 'asc',
      },
    });

    // get items with fallback group name
    const itemsWithFallbackGroupName = items.map((item) => {
      // get fallback group name
      if (!item.group_name) {
        // get first origin of inventory
        // because the inventory doesn't have group name, so it must have only one origin
        const origin = item.inventory.the_origins[0];

        // calc fallback group name
        const fallbackGroupName = `${dayjs
          .getDateFromJST(origin.arrival_date)
          .format('MM/DD')} ${origin.starting_enterprise.enterprise_name}`;
        delete item.inventory.the_origins;
        return {
          ...item,
          inventory: {
            ...item.inventory,
            fallback_group_name: fallbackGroupName,
          },
        };
      }
      // if inventory has group name, return it
      delete item.inventory.the_origins;
      return item;
    });

    return this.SUCCESS({
      total_item,
      items: itemsWithFallbackGroupName,
      page: tempPage,
      page_size: +limit,
    });
  }

  /**
   * Get inventory detail
   * @param {object} user - user information
   * @param {number} id - inventory id
   * @returns {Promise<object>} - response object { inventory }
   */
  async getInventoryDetail(user, id) {
    const connect = this.DB.READ;
    // get inventory
    const inventory = await connect.inventories.findUnique({
      where: {
        id: Number(id),
        user_id: user.id,
        delete_flag: false,
      },
      select: {
        id: true,
        group_name: true,
        net_weight_inventory: true,
        net_weight_total: true,
        gross_weight_inventory: true,
        tare_weight_inventory: true,
        latest_arrival_date: true,
        the_origins: {
          select: {
            id: true,
            code: true,
            starting_enterprise_name: true,
            starting_user_name: true,
          },
          where: {
            delete_flag: false,
            arrival_date: {
              not: null,
            },
          },
        },
      },
    });

    // if inventory not found, return error
    if (!inventory) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO);
    }

    // calc fallback group name
    if (!inventory.group_name) {
      // get first origin of inventory
      // because the inventory doesn't have group name, so it must have only one origin
      const origin = inventory.the_origins[0];
      // calc fallback group name
      const fallbackGroupName = `${dayjs
        .getDateFromJST(origin.arrival_date)
        .format('MM/DD')} ${origin.starting_enterprise_name}`;
      return this.SUCCESS({
        ...inventory,
        fallback_group_name: fallbackGroupName,
      });
    }

    // if inventory has group name, return it
    return this.SUCCESS(inventory);
  }

  /**
   * Get inventory edited detail
   * @param {object} user - user information
   * @param {number} id - inventory history id
   * @returns {Promise<object>} - response object { data: inventory, allowUndoChange: boolean }
   * @throws {object} - error object
   */
  async getInventoryEditedDetail(user, id) {
    const connect = this.DB.READ;

    // get inventory history
    const inventoryHistory = await connect.inventories_history.findUnique({
      where: {
        id: Number(id),
        user_id: user.id,
        delete_flag: false,
      },
      select: {
        id: true,
        net_weight_inventory: true,
        new_net_weight_inventory: true,
        created_on: true,
        inventory: {
          select: {
            id: true,
            group_name: true,
            is_history_cancel_locked: true,
            the_origins: {
              select: {
                id: true,
                code: true,
                starting_enterprise: {
                  select: {
                    enterprise_name: true,
                  },
                },
                starting_user: {
                  select: {
                    name: true,
                  },
                },
              },
              where: {
                delete_flag: false,
                arrival_date: {
                  not: null,
                },
              },
            },
          },
        },
        reason_diff: true,
        type_diff: true,
      },
    });

    // if inventory not found, return error
    if (!inventoryHistory) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO);
    }

    // check allow undo change
    let allowUndoChange = false;
    // inventory.is_history_cancel_locked = false => allow undo change
    if (!inventoryHistory.inventory.is_history_cancel_locked) {
      // find all inventory history of inventory to check last inventory history
      const inventoryHistoryList = await connect.inventories_history.findMany({
        where: {
          inventory_id: inventoryHistory.inventory.id,
          delete_flag: false,
        },
        select: {
          id: true,
        },
        orderBy: {
          created_on: 'desc',
        },
      });

      // if last inventory history is current inventory history, allow undo change
      if (inventoryHistoryList.length > 0) {
        const lastInventoryHistory = inventoryHistoryList[0];
        if (lastInventoryHistory.id === inventoryHistory.id) {
          allowUndoChange = true;
        }
      }
    }

    // calc fallback group name
    if (!inventoryHistory.inventory.group_name) {
      // get first origin of inventory
      // because the inventory doesn't have group name, so it must have only one origin
      const origin = inventoryHistory.inventory.the_origins[0];
      // calc fallback group name
      const fallbackGroupName = `${dayjs
        .getDateFromJST(origin.arrival_date)
        .format('MM/DD')} ${origin.starting_enterprise.enterprise_name}`;
      return this.SUCCESS({
        data: {
          ...inventoryHistory,
          inventory: {
            ...inventoryHistory.inventory,
            fallback_group_name: fallbackGroupName,
          },
        },
        allow_undo_change: allowUndoChange,
      });
    }

    return this.SUCCESS({
      data: inventoryHistory,
      allow_undo_change: allowUndoChange,
    });
  }

  /**
   * Edit inventory
   * @param {object} user - user information
   * @param {number} id - inventory id
   * @param {object} data - data for editing inventory
   * @returns {Promise<object>} - response object { inventory }
   * @throws {object} - error object
   */
  async editInventory(user, id, data) {
    const connect = this.DB.WRITE;
    // get inventory
    const inventory = await connect.inventories.findUnique({
      where: {
        id: Number(id),
        user_id: user.id,
        delete_flag: false,
      },
    });

    // if inventory not found, return error
    if (!inventory) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO);
    }

    if (+inventory.net_weight_inventory !== +data.grossWeightInventory) {
      throw new ControlledException(MESSAGE.MSG_DIFF_STOCK_ERROR);
    }

    if (
      data.grossWeightInventory - data.tareWeightInventory >
      inventory.net_weight_total
    ) {
      throw new ControlledException(MESSAGE.MSG_INCREASED_STOCK_ERROR);
    }

    // if inventory has been reset edit, return error
    if (!inventory.net_weight_inventory) {
      throw new ControlledException(MESSAGE.MSG_DIFF_STOCK_ERROR);
    }

    // check has been edited or not group name
    if (data.groupName && data.groupName !== inventory.group_name) {
      // check group name is existed
      const isGroupNameExisted = await this.#checkGroupNameExisted(
        user,
        data.groupName
      );
      if (isGroupNameExisted) {
        throw new ControlledException(MESSAGE.MSG_EXISTED_GROUP_NAME_ERROR);
      }
    }

    await connect.$transaction(async (tx) => {
      // create inventory-history
      await tx.inventories_history.create({
        data: {
          inventory_id: inventory.id,
          group_name: inventory.group_name,
          net_weight_inventory: inventory.net_weight_inventory,
          net_weight_total: inventory.net_weight_total,
          gross_weight_inventory: inventory.gross_weight_inventory,
          tare_weight_inventory: inventory.tare_weight_inventory,
          latest_arrival_date: inventory.latest_arrival_date,
          // can not reset edit after edit more than 1 time
          user_id: inventory.user_id,
          delete_flag: false,
          new_net_weight_inventory:
            data.grossWeightInventory - data.tareWeightInventory,
          new_gross_weight_inventory: data.grossWeightInventory,
          new_tare_weight_inventory: data.tareWeightInventory,
          type_diff: data.typeDiff,
          reason_diff: data.reasonDiff || undefined,
          created_by_id: user.id,
          latest_updated_on: dayjs().toDate(),
          latest_updated_by_id: user.id,
          created_on: dayjs().toDate(),
          is_display_inventories_history: true,
        },
      });

      // update inventory
      await tx.inventories.update({
        where: {
          id: inventory.id,
          delete_flag: false,
        },
        data: {
          group_name: data.groupName || null,
          net_weight_inventory:
            data.grossWeightInventory - data.tareWeightInventory,
          gross_weight_inventory: data.grossWeightInventory,
          tare_weight_inventory: data.tareWeightInventory,
          latest_updated_by_id: user.id,
          latest_updated_on: dayjs().toDate(),
          cancelable_from_date: dayjs().toDate(),
          is_history_cancel_locked: false,
        },
      });
    });

    // get inventory response
    const inventoryResponse = await connect.inventories.findUnique({
      where: {
        id: inventory.id,
        delete_flag: false,
      },
      select: {
        id: true,
        group_name: true,
        net_weight_inventory: true,
        net_weight_total: true,
        latest_arrival_date: true,
        the_origins: {
          select: {
            starting_enterprise: {
              select: {
                enterprise_name: true,
              },
            },
          },
          where: {
            delete_flag: false,
            arrival_date: {
              not: null,
            },
          },
        },
      },
    });

    return this.SUCCESS(inventoryResponse);
  }

  /**
   * Reset inventory
   * @param {object} user - user information
   * @param {number} id - inventory id
   * @returns {Promise<object>} - response object
   * @throws {object} - error object
   */
  async resetInventory(user, id) {
    const connect = this.DB.WRITE;

    // get inventory
    const inventory = await connect.inventories.findUnique({
      where: {
        id: Number(id),
        user_id: user.id,
        delete_flag: false,
      },
    });

    // if inventory not found, return error
    if (!inventory) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO);
    }

    // update inventory
    await connect.$transaction(async (tx) => {
      await tx.inventories.update({
        where: {
          id: inventory.id,
        },
        data: {
          net_weight_inventory: 0,
          gross_weight_inventory: 0,
          tare_weight_inventory: 0,
          latest_updated_by_id: user.id,
          latest_updated_on: dayjs().toDate(),
          cancelable_from_date: dayjs().toDate(),
          is_history_cancel_locked: true,
        },
      });

      // clear inventory-history
      await tx.inventories_history.updateMany({
        where: {
          inventory_id: inventory.id,
          delete_flag: false,
        },
        data: {
          delete_flag: true,
          latest_updated_by_id: user.id,
          latest_updated_on: dayjs().toDate(),
        },
      });
    });

    return this.SUCCESS();
  }

  /**
   * Undo inventory
   * @param {object} user - user information
   * @param {number} id - inventory history id
   * @returns {Promise<object>} - response object
   * @throws {object} - error object
   */
  async undoChangeInventory(user, id) {
    const connect = this.DB.WRITE;

    // get inventory history
    const inventoryHistory = await connect.inventories_history.findUnique({
      where: {
        id: Number(id),
        user_id: user.id,
        delete_flag: false,
      },
      include: {
        inventory: {
          select: {
            id: true,
            is_history_cancel_locked: true,
            group_name: true,
          },
        },
      },
    });

    // if inventory history not found, return error
    if (!inventoryHistory) {
      throw new ControlledException(MESSAGE.MSG_REVERSE_INVENTORY_ERROR);
    }

    // find all inventory history of inventory to check if this is the latest one
    const inventoryHistoryList = await connect.inventories_history.findMany({
      where: {
        inventory_id: inventoryHistory.inventory.id,
        delete_flag: false,
      },
      select: {
        id: true,
        created_on: true,
      },
      orderBy: {
        created_on: 'desc',
      },
    });

    // Check if there are any inventory history records
    if (inventoryHistoryList.length === 0) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO);
    }

    // Get the latest inventory history record
    const latestInventoryHistory = inventoryHistoryList[0];

    // Only allow undo if this is the most recently created inventory history
    if (latestInventoryHistory.id !== inventoryHistory.id) {
      throw new ControlledException(MESSAGE.MSG_NOT_LATEST_INVENTORY_HISTORY_ERROR);
    }

    // check has been edited or not group name
    if (
      inventoryHistory.group_name &&
      inventoryHistory.group_name !== inventoryHistory.inventory.group_name
    ) {
      // check group name is existed
      const isGroupNameExisted = await this.#checkGroupNameExisted(
        user,
        inventoryHistory.group_name
      );
      if (isGroupNameExisted) {
        throw new ControlledException(MESSAGE.MSG_EXISTED_GROUP_NAME_ERROR);
      }
    }

    await connect.$transaction(async (tx) => {
      // update inventory
      await tx.inventories.update({
        where: {
          id: inventoryHistory.inventory.id,
          delete_flag: false,
        },
        data: {
          group_name: inventoryHistory.group_name,
          net_weight_inventory: inventoryHistory.net_weight_inventory,
          gross_weight_inventory: inventoryHistory.gross_weight_inventory,
          tare_weight_inventory: inventoryHistory.tare_weight_inventory,
          latest_updated_by_id: user.id,
          latest_updated_on: dayjs().toDate(),
          is_history_cancel_locked: true,
        },
      });

      // delete inventory-history
      await tx.inventories_history.update({
        where: {
          id: inventoryHistory.id,
          delete_flag: false,
        },
        data: {
          delete_flag: true,
          latest_updated_by_id: user.id,
          latest_updated_on: dayjs().toDate(),
        },
      });
    });

    return this.SUCCESS();
  }
}

module.exports = InventoryManagementService;
