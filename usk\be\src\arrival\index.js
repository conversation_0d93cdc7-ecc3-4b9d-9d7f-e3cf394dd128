// ======== IMPORT ========================
const {
  getQrArrivalSchema,
  registerManualSchema,
  registerByQrSchema,
  getArrivalListSchema,
  getArrivalDetailSchema,
  editArrivalSchema,
  cancelArrivalSchema,
} = require('./schema');
const { auth, apiKeyVerify, authEnterprise, authUserStatus } = require('../base/authorized');

// ===== 1. Injection ==============

// ===== 2. Router Functions ==============
async function getQrImportHandle(request, reply) {
  const arrivalService = request.diScope.resolve('arrivalService');
  const { query, user } = request;
  const response = await arrivalService.getQrImport(user, query.qrCode);
  return reply.send(response);
}

async function registerArrivalManualHandle(request, reply) {
  const arrivalService = request.diScope.resolve('arrivalService');
  const { body, user } = request;
  const response = await arrivalService.registerArrivalManual(user, body);
  return reply.send(response);
}

async function registerArrivalByQrHandle(request, reply) {
  const arrivalService = request.diScope.resolve('arrivalService');
  const { body, user } = request;
  const response = await arrivalService.registerArrivalByQr(user, body);
  return reply.send(response);
}

async function getListArrivalHandle(request, reply) {
  const arrivalService = request.diScope.resolve('arrivalService');
  const { query, user } = request;
  const response = await arrivalService.getListArrival(query, user);
  return reply.send(response);
}

async function getArrivalDetailHandler(request, reply) {
  const arrivalService = request.diScope.resolve('arrivalService');
  const { params, user } = request;
  const response = await arrivalService.getArrivalDetail(params.id, user);
  return reply.send(response);
}

async function editArrivalHandler(request, reply) {
  const arrivalService = request.diScope.resolve('arrivalService');
  const { params, user, body } = request;
  const response = await arrivalService.editArrival(user, params.id, body);
  return reply.send(response);
}

async function cancelArrivalHandler(request, reply) {
  const arrivalService = request.diScope.resolve('arrivalService');
  const { params, user } = request;
  const response = await arrivalService.cancelArrival(user, params.id);
  return reply.send(response);
}

// ===== 3. Router Registration ============
module.exports = async (fastify) => {
  fastify.get(
    '/detail-qr',
    {
      schema: getQrArrivalSchema,
      onRequest: [apiKeyVerify, auth, authEnterprise, authUserStatus],
    },
    getQrImportHandle
  );
  fastify.post(
    '/register-manual',
    {
      schema: registerManualSchema,
      onRequest: [apiKeyVerify, auth, authEnterprise, authUserStatus],
    },
    registerArrivalManualHandle
  );
  fastify.post(
    '/register-by-qr',
    {
      schema: registerByQrSchema,
      onRequest: [apiKeyVerify, auth, authEnterprise, authUserStatus],
    },
    registerArrivalByQrHandle
  );
  fastify.get(
    '/arrival-list',
    {
      schema: getArrivalListSchema,
      onRequest: [apiKeyVerify, auth, authEnterprise],
    },
    getListArrivalHandle
  );
  fastify.get(
    '/arrival-detail/:id',
    {
      schema: getArrivalDetailSchema,
      onRequest: [apiKeyVerify, auth, authEnterprise],
    },
    getArrivalDetailHandler
  );
  fastify.post(
    '/edit-arrival/:id',
    {
      schema: editArrivalSchema,
      onRequest: [apiKeyVerify, auth, authEnterprise, authUserStatus],
    },
    editArrivalHandler
  );
  fastify.post(
    '/cancel-arrival/:id',
    {
      schema: cancelArrivalSchema,
      onRequest: [apiKeyVerify, auth, authEnterprise, authUserStatus],
    },
    cancelArrivalHandler
  );
};
