<template>
  <div class="tw:pb-[20rem] tw:tl:pb-0 tw:dt:pb-[8rem]">
    <q-card class="tw:mb-4 tw:bg-white tw:p-4 tw:min-h-[calc(100vh-15rem)]">
      <div class="tw:text-l-design tw:font-[700] tw:mb-3">取引先詳細</div>
      <div class="tw:border tw:border-[#D2D2D2] tw:rounded-none tw:tl:mb-[10rem]">
        <div class="tw:flex tw:flex-col tw:divide-y-2 tw:divide-[#D2D2D2]">
          <!-- businessType -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:bg-[#E2E3EA] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              ユーザーID
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design tw:min-h-[40px]">
              {{ FORMAT_ID_NUMBER(partnerDetail?.partner?.user_code) }}
            </div>
          </div>

          <!-- prefecture -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:bg-[#E2E3EA] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              事業者/従事者区分
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design tw:min-h-[40px]">
              {{ showRole(partnerDetail?.partner) }}
            </div>
          </div>

          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:bg-[#E2E3EA] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              都道府県
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design tw:min-h-[40px]">
              {{ partnerDetail?.partner?.provinces?.name }}
            </div>
          </div>

          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:bg-[#E2E3EA] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              届出事業者名
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design tw:min-h-[40px]">
              {{ partnerDetail?.partner?.enterprise?.enterprise_name }}
            </div>
          </div>

          <!-- enterprise name -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:bg-[#E2E3EA] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              事業者名
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design tw:min-h-[40px]">
              {{ partnerDetail?.partner?.name }}
            </div>
          </div>

          <!-- enterprise name kana -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:bg-[#E2E3EA] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              事業者名（カナ）
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design tw:min-h-[40px]">
              {{ partnerDetail?.partner?.name_kana }}
            </div>
          </div>

          <!-- enterprise code -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:bg-[#E2E3EA] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              届出番号
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design tw:min-h-[40px]">
              {{ partnerDetail?.partner?.enterprise?.enterprise_code }}
            </div>
          </div>

          <div
            v-if="
              CHECK_ROLE(
                [ROLES_ENUM.NORMAL_USER],
                [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
                [],
                partnerDetail?.partner
              )
            "
            class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full"
          >
            <div
              class="tw:tl:w-[30%] tw:bg-[#E2E3EA] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              許可番号
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design tw:min-h-[40px]">
              {{ partnerDetail?.partner?.license_number }}
            </div>
          </div>

          <!-- partner type -->
          <div
            v-if="!editMode"
            class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full"
          >
            <div
              class="tw:tl:w-[30%] tw:bg-[#E2E3EA] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              取引先区分
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design tw:min-h-[40px]">
              <div class="">
                {{
                  (partnerDetail?.partner_type || [])
                    .map(
                      (i) =>
                        partnerTypeOptions.find(
                          (option) => option.value === i.toString()
                        )?.label
                    )
                    .filter(Boolean)
                    .join(' / ')
                }}
              </div>
            </div>
          </div>
          <div v-else class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:bg-[#E2E3EA] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              取引先区分
              <q-badge
                class="tw:text-white tw:bg-[#E80F00] tw:p-1 tw:rounded tw:text-xxs-design tw:justify-center tw:items-center tw:flex tw:tl:ml-1"
              >
                必須
              </q-badge>
            </div>
            <div
            :class="!!errors.partnerType ? 'tw:pb-0' : ''"
            class="tw:tl:w-[70%] tw:p-4 tw:text-m-design">
              <q-field
                :error="!!errors.partnerType"
                no-error-icon
                borderless
                hide-bottom-space
              >
                <template v-slot:control>
                  <div class="tw:flex tw:w-full tw:gap-x-3">
                    <q-checkbox
                      class="tw:text-m-design"
                      v-for="(item, index) in partnerTypeOptions"
                      :key="index"
                      v-model="form.partnerType"
                      :disable="disabledPartnerTypes[item.value]"
                      :label="item.label"
                      :val="item.value"
                      size="lg"
                    />
                  </div>
                </template>
                <template v-slot:error>
                  <div class="tw:pl-6 tw:xl:pl-4 tw:transform tw:-translate-y-4">
                    {{ errors.partnerType }}
                  </div>
                </template>
              </q-field>
            </div>
          </div>
        </div>
      </div>

      <!-- Buttons -->
      <q-footer
        elevated
        class="tw:bg-white tw:p-3 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)]
        tw:w-full tw:items-center tw:flex tw:justify-center tw:tl:justify-between
        tw:min-h-[91px] tw:tl:h-[6.5rem] tw:flex-col tw:gap-4 tw:tl:flex-row"
      >
        <BaseButton
          v-if="!editMode"
          outline
          class="tw:rounded-[40px]"
          :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[21.2rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-full`"
          label="取引先管理に戻る"
          @click.prevent="goToPage('partner')"
        />
        <div
          v-if="!editMode"
          class="tw:gap-4 tw:flex tw:flex-col tw:tl:flex-row tw:justify-end tw:w-full tw:tl:w-[50rem]"
        >
          <BaseButton
            outline
            class="tw:rounded-[40px]"
            :class="`tw:bg-white tw:text-[#E80F00] tw:text-m-design tw:tl:font-bold
      tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-full`"
            label="削除する"
            @click.prevent="handleClickDelete"
          />
          <BaseButton
            outline
            class="tw:rounded-[40px]"
            :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-full`"
            label="修正する"
            @click.prevent="hanClickEdit"
          />
        </div>
        <BaseButton
          v-if="editMode"
          outline
          class="tw:rounded-[40px]"
          :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[20.5rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[59px]
      tw:w-full`"
          label="修正をやめる"
          @click.prevent="handleStopEdit"
        />
        <BaseButton
          v-if="editMode"
          outline
          class="tw:rounded-[40px]"
          :class="`tw:bg-blue-3 tw:text-white tw:text-m-design tw:tl:font-bold
      tw:tl:w-[24.5rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[59px]
      tw:w-full`"
          label="確認する"
          @click.prevent="hanClickEdit"
        />
      </q-footer>
    </q-card>
  </div>

  <PopupConfirmText />
  <PopupConfirmPartner />
</template>
<script setup>
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import locatedTransactionService from 'services/locatedTransaction.service';
import { ref, onMounted, watch, provide, computed } from 'vue';
import { PARTNER_TYPE_ENUM } from 'helpers/constants';
import { useRouter } from 'vue-router';
import {
  calcDisabledRadioButtonPartnerType,
  CHECK_ROLE,
  FORMAT_ID_NUMBER,
  showRole,
} from 'src/helpers/common';
import PopupConfirmPartner from 'components/PopupConfirmPartner.vue';
import PopupConfirmText from 'components/PopupConfirmText.vue';
import { useAuthStore } from 'src/stores/auth-store';
import toast from 'src/shared/utilities/toast';
import useValidate from 'composables/validate';
import MESSAGE from 'src/helpers/message';
import updateLocatedTransactionSchema from 'src/schemas/located-transaction/updateLocatedTransaction.schema';
import BaseButton from 'src/components/base/vs/BaseButton.vue';
import {
  ENTERPRISE_TYPE_ENUM,
  ROLES_ENUM,
  TYPE_STAFF,
} from 'src/helpers/constants';

const router = useRouter();
const partnerDetail = ref(null);
const { colorSub } = storeToRefs(useAppStore());
const { user } = storeToRefs(useAuthStore());
const partnerTypeOptions = [
  {
    label: '仕入先',
    value: PARTNER_TYPE_ENUM.SUPPLIER.toString(),
  },
  {
    label: '出荷先',
    value: PARTNER_TYPE_ENUM.SHIPPER.toString(),
  },
];

const disabledPartnerTypes = computed(() => {
  const currentUser = user.value;
  const partner = partnerDetail.value;

  const disabled = {
    [PARTNER_TYPE_ENUM.SUPPLIER.toString()]: false,
    [PARTNER_TYPE_ENUM.SHIPPER.toString()]: false,
  };

  if (partner?.partner.enterprise_type === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE) {
    disabled[PARTNER_TYPE_ENUM.SUPPLIER.toString()] = true;
  }

  if (currentUser?.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE &&
      partner?.partner.enterprise_type === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE) {
    disabled[PARTNER_TYPE_ENUM.SUPPLIER.toString()] = true;
  }
  if (currentUser?.enterprise_type === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE &&
      partner?.partner.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE) {
    disabled[PARTNER_TYPE_ENUM.SHIPPER.toString()] = true;
  }

  return disabled;
});

const editMode = ref(false);
const disableRadioButtons = ref({});
const form = ref({
  partnerType: [],
});
const isShowPopup = ref(false);
const { validateData, errors } = useValidate();
const renderStaffType = (enterpriseType, staffType) => {
  let value = staffType;
  if (enterpriseType === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE) {
    value = 0;
  }
  return TYPE_STAFF[value];
};
// #region methods
// convert partner type to string for display
// (ex: 1,2 => 仕入先/出荷先)
const convertPartnerType = partnerType =>
  partnerTypeOptions
    .reduce((init, t) => {
      if (partnerType.includes(t.value)) {
        init.push(t.label);
      }
      return init;
    }, [])
    .join('/');

const hanClickEdit = () => {
  // for the first time click edit button
  if (!editMode.value) {
    editMode.value = true;
    router.push({ query: { edit: 'true' } });
  } else {
    // for the second time click edit button
    const partnerType = form.value.partnerType.map(item =>
      parseInt(item, 10));
    const payload = {
      partnerType,
    };
    const validate = validateData(updateLocatedTransactionSchema, payload);
    if (validate) {
      popupConfirmItems.value.listItems = [
        {
          key: '取引先区分',
          badge: true,
          value: convertPartnerType(form.value.partnerType),
        },
      ];

      popupConfirmItems.value.isPopup = true;
    }
  }
};

const handleStopEdit = () => {
  editMode.value = false;
  router.push({ query: { edit: 'false' } });
};

const goToPage = name => {
  router.push({ name });
};

const handleClickConfirmEdit = async () => {
  const partnerType = form.value.partnerType.map(item => parseInt(item, 10));
  const payload = {
    partnerType,
  };

  const response = await locatedTransactionService.updateLocatedTransaction(
    partnerDetail.value?.id,
    payload
  );
  if (response.code === 0) {
    toast.access(MESSAGE.MSG_FIX_CUSTOMER_INFO);
    await router.push({ name: 'partner' });
  }
};

const handleClickDelete = async () => {
  isShowPopup.value = true;
};
// #endregion

// #region watch
// Watch for changes in disabled partner types and automatically remove disabled options from selection
watch(disabledPartnerTypes, newDisabled => {
  // Remove any selected options that are now disabled
  form.value.partnerType = form.value.partnerType.filter(selectedValue => !newDisabled[selectedValue]);
}, { deep: true });

watch(
  () => router.currentRoute.value.query?.edit,
  value => {
    if (value === 'true') {
      editMode.value = true;

      form.value.partnerType =
        partnerDetail.value?.partner_type?.map(item => item.toString()) || [];
    } else {
      editMode.value = false;
    }
  }
);
// #endregion

// #region provide
const popupConfirmTextProvideData = {
  isPopup: isShowPopup,
  titlePopup: '取引先削除',
  caption: '取引先を削除します。よろしいですか？',
  handleCloseModal: () => {
    isShowPopup.value = false;
  },
  handleAcceptModal: async () => {
    isShowPopup.value = false;
    const response = await locatedTransactionService.deleteLocatedTransaction(
      partnerDetail.value?.id
    );

    if (response.code === 0) {
      toast.access(MESSAGE.MSG_DELETE_CUSTOMER_INFO);
      await router.push({ name: 'partner' });
    }
  },
};
provide('popupConfirmTextProvideData', popupConfirmTextProvideData);

const popupConfirmItems = ref({
  isPopup: false,
  titlePopup: '修正確認',
  captionPopup: '以下の内容で、取引先を修正します',
  confirmFunc: handleClickConfirmEdit,
  listItems: [],
  minWidthDefault: 70,
  minWidthTlDefault: 100,
  minWidthDtDefault: 153,
});
provide('popupConfirmItems', popupConfirmItems);
// #endregion

// #region lifecycle hooks
onMounted(async () => {
  const partnerId = router.currentRoute.value?.params?.id;
  if (!partnerId) {
    await router.push({ name: 'partner' });
  }
  editMode.value = router.currentRoute.value.query?.edit === 'true';

  const partnerDetailResponse =
    await locatedTransactionService.getLocatedTransactionDetail(partnerId);
  partnerDetail.value = partnerDetailResponse.payload;
  disableRadioButtons.value = calcDisabledRadioButtonPartnerType(
    user.value?.enterprise_type,
    partnerDetail.value?.partner?.type
  );
  form.value.partnerType =
    partnerDetail.value?.partner_type?.map(item => item.toString()) || [];
});
// #endregion
</script>
