<template>
  <q-card class="tw:flex tw:flex-col tw:h-full tw:pb-[22rem] tw:tl:pb-[8rem]">
    <div class="tw:text-s-design tw:text-[#333333] tw:p-4">
      スキャナーで、利用者カードのQRコードもしくは入出荷用のQRコードをスキャンしてください。
    </div>
    <div class="tw:flex tw:items-center tw:justify-center tw:flex-1 tw:pb-[4rem]">
      <span class="tw:relative tw:flex tw:w-32 tw:h-32">
        <span
          class="tw:animate-ping tw:absolute tw:h-full tw:w-full tw:inline-flex tw:rounded-full tw:bg-sky-400 tw:opacity-75"
        ></span>
        <span
          class="tw:relative tw:inline-flex tw:rounded-full tw:w-32 tw:h-32 tw:bg-sky-300"
        ></span>
      </span>
    </div>
    <!-- Footer Button -->
    <q-footer
      elevated
      class="tw:bg-white tw:p-4 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)]
      tw:w-full tw:tl:justify-start tw:tl:items-center tw:flex tw:justify-center
      tw:mt-4 tw:flex-col tw:gap-8 tw:tl:flex-row"
    >
      <div class="tw:text-[#333333] tw:text-m-design tw:font-normal">入荷登録方法</div>
      <div class="tw:flex tw:gap-4 tw:tl:gap-8">
        <BaseButton
          outline
          class="tw:rounded-[40px]"
          :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
          tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
          tw:w-full`"
          label="カメラ"
          @click.prevent="handleClickQrCamera"
        />
      </div>

      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
        tw:tl:w-[20.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
        tw:w-full`"
        label="伝票から手入力"
        @click.prevent="handleClickManualRegistration"
      />
    </q-footer>
  </q-card>
</template>
<script setup>
import { ref, onMounted, watch, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import useValidate from 'composables/validate';
import arrivalService from 'services/arrival.service';
import authService from 'services/auth.service';
import toast from 'utilities/toast';
import MESSAGE from 'src/helpers/message';
import dayjs from 'boot/dayjs';
import { useAuthStore } from 'stores/auth-store';
import { debounce } from 'lodash';
import BaseButton from 'components/base/vs/BaseButton.vue';
import commonService from 'src/shared/services/common.service';
import { checkQrArrivalSchema, checkQrUserSchema } from 'src/schemas/arrival/qrChecking.schema';
import { OPTION_TYPE_ENUM } from 'src/helpers/constants';

const { validateData } = useValidate();
const router = useRouter();

// ===== REF =====
const qrCodeContent = ref('');
const dateStartScan = ref(dayjs());
const intervalFn = ref();
const { signInProxyUser } = useAuthStore();

// ===== METHOD =====
const handleRegisterArrival = debounce(async qrCode => {
  const result = await arrivalService.getDetailQrArrival({ qrCode });
  if (result.code === 402) {
    router.push('home');
    return;
  }
  if (result.code === 401) {
    return;
  }
  if (result.code === 0) {
    router.push({ name: 'arrivalRegistration', params: { qrCode } });
  } else {
    toast.error(result.message);
    clearInterval(intervalFn.value);
    qrCodeContent.value = '';
  }
}, 700);

const handleLoginProxyUser = debounce(async qrCode => {
  const result = await authService.loginInsteadLink({ qrCode });
  if (result.code === 402) {
    router.push('home');
    return;
  }
  if (result.code === 401) {
    return;
  }
  if (result.code === 0) {
    router.push({ name: 'registerProxyOutboundShipment' });
  } else {
    toast.error(result.message);
    clearInterval(intervalFn.value);
    qrCodeContent.value = '';
  }
}, 700);

const checkTimeScanQrCode = () => {
  const currentTime = dayjs();
  const diffInSeconds = currentTime.diff(dateStartScan.value, 'second');
  if (diffInSeconds > 5) {
    clearInterval(intervalFn.value);
    qrCodeContent.value = '';
    toast.error(MESSAGE.MSG_LIMITS_READTIMEQR_ERROR);
  }
};

const handleScanValue = async ev => {
  qrCodeContent.value = ev;
  clearInterval(intervalFn.value);
  intervalFn.value = setInterval(checkTimeScanQrCode, 1000);

  // check qr code for arrival
  const validArrival = validateData(checkQrArrivalSchema, {
    qrCode: qrCodeContent.value,
  });
  if (validArrival) {
    clearInterval(intervalFn.value);
    const qrCode = qrCodeContent.value;
    await handleRegisterArrival(qrCode);
    return;
  }

  // check qr code for proxy user
  const validProxyLogin = validateData(checkQrUserSchema, {
    qrCode: qrCodeContent.value,
  });
  if (validProxyLogin) {
    clearInterval(intervalFn.value);
    const qrCode = qrCodeContent.value.slice(-16);
    await handleLoginProxyUser(qrCode);
    return;
  }
};

const handleClickQrCamera = () => {
  router.push({ name: 'arrivalQrCamera' });
};

const handleClickManualRegistration = async () => {
  const partnerOptionsResponse = await commonService.getOptions({
    type: OPTION_TYPE_ENUM.USER_SUPPLIER,
  });
  if (partnerOptionsResponse.payload?.length === 0) {
    toast.error(MESSAGE.MSG_NA_CUSTOMER_ERR);
  } else {
    router.push({ name: 'manualRegistration' });
  }
};

let timer;
const scanDelay = 50;
let buffer = '';
const handleScan = e => {
  if (e.key === 'Shift') {
    return;
  }
  buffer += e.key;
  clearTimeout(timer);
  timer = setTimeout(() => {
    if (buffer) {
      const qrCode = buffer;
      if (buffer !== 'Meta') {
        handleScanValue(qrCode);
      }
      buffer = '';
      clearTimeout(timer);
    }
  }, scanDelay);
};

// initialization
onMounted(() => {
  signInProxyUser(null);
  document.addEventListener('keydown', handleScan);
});

onUnmounted(() => {
  clearInterval(intervalFn.value);
  clearTimeout(timer);
  document.removeEventListener('keydown', handleScan);
});

watch(qrCodeContent, (_, oldVal) => {
  if (!oldVal) {
    dateStartScan.value = dayjs();
  }
});
</script>
