-- public.catch_records definition

-- Drop table

-- DROP TABLE public.catch_records;

CREATE TABLE public.catch_records (
	id serial4 NOT NULL,
	catch_date text NOT NULL,
	destination_user_id int4 NOT NULL,
	destination_enterprise_name varchar(256) NOT NULL,
	destination_user_name varchar(256) NOT NULL,
	destination_license_number varchar(256) NOT NULL,
	destination_user_note_1 varchar(256) NOT NULL,
	destination_user_note_2 varchar(256) NOT NULL,
	starting_user_id int4 NOT NULL,
	starting_enterprise_name varchar(256) NOT NULL,
	starting_user_name varchar(256) NOT NULL,
	starting_license_number varchar(256) NOT NULL,
	starting_user_note_1 varchar(256) NOT NULL,
	starting_user_note_2 varchar(256) NOT NULL,
	catch_weight numeric(65, 2) NOT NULL,
	operating_days int4 NOT NULL,
	CONSTRAINT catch_records_pkey PRIMARY KEY (id)
);
-- Indexes for catch_records
CREATE INDEX catch_records_catch_date_idx ON public.catch_records USING btree (catch_date);
CREATE INDEX catch_records_destination_user_id_idx ON public.catch_records USING btree (destination_user_id);
CREATE INDEX catch_records_starting_user_id_idx ON public.catch_records USING btree (starting_user_id);

-- public.code_suffixes_alloc definition

-- Drop table

-- DROP TABLE public.code_suffixes_alloc;

CREATE TABLE public.code_suffixes_alloc (
	id serial4 NOT NULL,
	code_group_key varchar(13) NOT NULL,
	code_suffix varchar(3) NOT NULL,
	alloc_status int4 DEFAULT 0 NOT NULL,
	created_by_id int4 DEFAULT 0 NOT NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NOT NULL,
	latest_updated_on timestamp(3) NULL,
	CONSTRAINT code_suffixes_alloc_pkey PRIMARY KEY (id)
);
-- Indexes for code_suffixes_alloc
CREATE INDEX code_suffixes_alloc_code_group_key_idx ON public.code_suffixes_alloc USING btree (code_group_key);
CREATE INDEX code_suffixes_alloc_code_suffix_idx ON public.code_suffixes_alloc USING btree (code_suffix);
CREATE INDEX code_suffixes_alloc_alloc_status_idx ON public.code_suffixes_alloc USING btree (alloc_status);
CREATE INDEX code_suffixes_alloc_created_on_idx ON public.code_suffixes_alloc USING btree (created_on);
-- Composite index for batch deletion query
CREATE INDEX code_suffixes_alloc_status_created_on_idx ON public.code_suffixes_alloc USING btree (alloc_status, created_on);


-- public.enterprises definition

-- Drop table

-- DROP TABLE public.enterprises;

CREATE TABLE public.enterprises (
	id serial4 NOT NULL,
	enterprise_code varchar(256) NOT NULL,
	enterprise_name varchar(256) NOT NULL,
	enterprise_name_kana varchar(256) NULL,
	enterprise_name_nospace varchar(256) NOT NULL,
	enterprise_name_kana_nospace varchar(256) NULL,
	"type" int4 NOT NULL,
	created_by_id int4 DEFAULT 0 NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NULL,
	latest_updated_on timestamp(3) NULL,
	delete_flag bool DEFAULT false NOT NULL,
	CONSTRAINT enterprises_pkey PRIMARY KEY (id)
);
-- Indexes for enterprises
CREATE INDEX enterprises_enterprise_name_nospace_idx ON public.enterprises USING btree (enterprise_name_nospace);
CREATE INDEX enterprises_enterprise_code_idx ON public.enterprises USING btree (enterprise_code);
CREATE INDEX enterprises_enterprise_name_idx ON public.enterprises USING btree (enterprise_name);
CREATE INDEX enterprises_enterprise_name_kana_nospace_idx ON public.enterprises USING btree (enterprise_name_kana_nospace);
CREATE INDEX enterprises_type_idx ON public.enterprises USING btree (type);
CREATE INDEX enterprises_delete_flag_idx ON public.enterprises USING btree (delete_flag);
CREATE INDEX enterprises_created_by_id_idx ON public.enterprises USING btree (created_by_id);


-- public.inventories definition

-- Drop table

-- DROP TABLE public.inventories;

CREATE TABLE public.inventories (
	id serial4 NOT NULL,
	group_name varchar(256) NULL,
	net_weight_inventory numeric(65, 2) NOT NULL,
	net_weight_total numeric(65, 2) NOT NULL,
	latest_arrival_date timestamp(3) NOT NULL,
	delete_flag bool DEFAULT false NOT NULL,
	created_by_id int4 DEFAULT 0 NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NOT NULL,
	latest_updated_on timestamp(3) NULL,
	gross_weight_inventory numeric(65, 2) NOT NULL,
	tare_weight_inventory numeric(65, 2) NOT NULL,
	user_id int4 NOT NULL,
	cancelable_from_date timestamp(3) NULL,
	inventory_start_date timestamp(3) NULL,
	inventory_type int4 DEFAULT 0 NOT NULL,
	is_history_cancel_locked bool DEFAULT true NOT NULL,
	current_arrival_id_list jsonb DEFAULT '[]'::jsonb NULL,
	CONSTRAINT inventories_pkey PRIMARY KEY (id)
);
-- Indexes for inventories
CREATE INDEX inventories_latest_arrival_date_idx ON public.inventories USING btree (latest_arrival_date);
CREATE INDEX inventories_user_id_idx ON public.inventories USING btree (user_id);
CREATE INDEX inventories_delete_flag_idx ON public.inventories USING btree (delete_flag);
CREATE INDEX inventories_group_name_idx ON public.inventories USING btree (group_name);
CREATE INDEX inventories_net_weight_inventory_idx ON public.inventories USING btree (net_weight_inventory);
CREATE INDEX inventories_net_weight_total_idx ON public.inventories USING btree (net_weight_total);
CREATE INDEX inventories_inventory_type_idx ON public.inventories USING btree (inventory_type);
CREATE INDEX inventories_cancelable_from_date_idx ON public.inventories USING btree (cancelable_from_date);
CREATE INDEX inventories_inventory_start_date_idx ON public.inventories USING btree (inventory_start_date);
CREATE INDEX inventories_is_history_cancel_locked_idx ON public.inventories USING btree (is_history_cancel_locked);


-- public.inventories_history definition

-- Drop table

-- DROP TABLE public.inventories_history;

CREATE TABLE public.inventories_history (
	id serial4 NOT NULL,
	inventory_id int4 NOT NULL,
	group_name varchar(256) NULL,
	net_weight_inventory numeric(65, 2) NOT NULL,
	net_weight_total numeric(65, 2) NOT NULL,
	latest_arrival_date timestamp(3) NOT NULL,
	delete_flag bool DEFAULT false NOT NULL,
	created_by_id int4 DEFAULT 0 NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NOT NULL,
	latest_updated_on timestamp(3) NULL,
	gross_weight_inventory numeric(65, 2) NOT NULL,
	new_gross_weight_inventory numeric(65, 2) NOT NULL,
	new_net_weight_inventory numeric(65, 2) NOT NULL,
	new_tare_weight_inventory numeric(65, 2) NOT NULL,
	tare_weight_inventory numeric(65, 2) NOT NULL,
	user_id int4 NOT NULL,
	type_diff int4 NULL,
	reason_diff varchar(256) NULL,
	current_arrival_id_list jsonb DEFAULT '[]'::jsonb NOT NULL,
	is_display_inventories_history bool DEFAULT true NOT NULL,
	new_current_arrival_id_list jsonb DEFAULT '[]'::jsonb NOT NULL,
	CONSTRAINT inventories_history_pkey PRIMARY KEY (id)
);
-- Indexes for inventories_history
CREATE INDEX inventories_history_inventory_id_idx ON public.inventories_history USING btree (inventory_id);
CREATE INDEX inventories_history_user_id_idx ON public.inventories_history USING btree (user_id);
CREATE INDEX inventories_history_created_by_id_idx ON public.inventories_history USING btree (created_by_id);
CREATE INDEX inventories_history_created_on_idx ON public.inventories_history USING btree (created_on);
CREATE INDEX inventories_history_latest_arrival_date_idx ON public.inventories_history USING btree (latest_arrival_date);
CREATE INDEX inventories_history_delete_flag_idx ON public.inventories_history USING btree (delete_flag);
CREATE INDEX inventories_history_type_diff_idx ON public.inventories_history USING btree (type_diff);
CREATE INDEX inventories_history_is_display_idx ON public.inventories_history USING btree (is_display_inventories_history);


-- public.licenses definition

-- Drop table

-- DROP TABLE public.licenses;

CREATE TABLE public.licenses (
	id serial4 NOT NULL,
	license_code varchar(256) NOT NULL,
	user_id int4 NULL,
	expiry_date timestamp(3) NULL,
	created_by_id int4 DEFAULT 0 NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NULL,
	latest_updated_on timestamp(3) NULL,
	delete_flag bool DEFAULT false NOT NULL,
	license_status int4 NULL,
	expiry_date_start timestamp(3) NULL,
	CONSTRAINT licenses_pkey PRIMARY KEY (id)
);
-- Indexes for licenses
CREATE UNIQUE INDEX licenses_license_code_key ON public.licenses USING btree (license_code);
CREATE INDEX licenses_user_id_idx ON public.licenses USING btree (user_id);
CREATE INDEX licenses_expiry_date_idx ON public.licenses USING btree (expiry_date);
CREATE INDEX licenses_expiry_date_start_idx ON public.licenses USING btree (expiry_date_start);
CREATE INDEX licenses_license_status_idx ON public.licenses USING btree (license_status);
CREATE INDEX licenses_delete_flag_idx ON public.licenses USING btree (delete_flag);

-- public.notifications definition

-- Drop table

-- DROP TABLE public.notifications;

CREATE TABLE public.notifications (
	id serial4 NOT NULL,
	title varchar(256) NULL,
	"content" text NULL,
	date_send timestamp(3) NOT NULL,
	created_by_id int4 DEFAULT 0 NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NOT NULL,
	latest_updated_on timestamp(3) NULL,
	delete_flag bool DEFAULT false NOT NULL,
	role_notify _varchar NULL,
	CONSTRAINT notifications_pkey PRIMARY KEY (id)
);
-- Indexes for notifications
CREATE INDEX notifications_title_idx ON public.notifications USING btree (title);
CREATE INDEX notifications_date_send_idx ON public.notifications USING btree (date_send);
CREATE INDEX notifications_created_by_id_idx ON public.notifications USING btree (created_by_id);
CREATE INDEX notifications_created_on_idx ON public.notifications USING btree (created_on);
CREATE INDEX notifications_delete_flag_idx ON public.notifications USING btree (delete_flag);
CREATE INDEX notifications_role_notify_idx ON public.notifications USING gin (role_notify);


-- public.partners definition

-- Drop table

-- DROP TABLE public.partners;

CREATE TABLE public.partners (
	id serial4 NOT NULL,
	user_id int4 NOT NULL,
	partner_id int4 NOT NULL,
	hidden_flag bool DEFAULT false NOT NULL,
	created_by_id int4 DEFAULT 0 NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NULL,
	latest_updated_on timestamp(3) NULL,
	delete_flag bool DEFAULT false NOT NULL,
	partner_type _int4 DEFAULT ARRAY[]::integer[] NULL,
	CONSTRAINT partners_pkey PRIMARY KEY (id),
	CONSTRAINT partners_unique UNIQUE (user_id, partner_id)
);
-- Indexes for partners
CREATE INDEX partners_partner_id_idx ON public.partners USING btree (partner_id);
CREATE INDEX partners_user_id_idx ON public.partners USING btree (user_id);
CREATE INDEX partners_hidden_flag_idx ON public.partners USING btree (hidden_flag);
CREATE INDEX partners_delete_flag_idx ON public.partners USING btree (delete_flag);
CREATE INDEX partners_partner_type_idx ON public.partners USING gin (partner_type);


-- public.provinces definition

-- Drop table

-- DROP TABLE public.provinces;

CREATE TABLE public.provinces (
	id serial4 NOT NULL,
	"name" varchar(300) NULL,
	setting json NOT NULL,
	created_by_id int4 DEFAULT 0 NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NULL,
	latest_updated_on timestamp(3) NULL,
	delete_flag bool DEFAULT false NOT NULL,
	region_id int4 DEFAULT 0 NOT NULL,
	CONSTRAINT provinces_pkey PRIMARY KEY (id)
);
-- Indexes for provinces
CREATE INDEX provinces_name_idx ON public.provinces USING btree (name);
CREATE INDEX provinces_region_id_idx ON public.provinces USING btree (region_id);
CREATE INDEX provinces_delete_flag_idx ON public.provinces USING btree (delete_flag);

-- public.readers definition

-- Drop table

-- DROP TABLE public.readers;

CREATE TABLE public.readers (
	id serial4 NOT NULL,
	notification_id int4 NOT NULL,
	reader_id int4 NOT NULL,
	created_by_id int4 DEFAULT 0 NOT NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NOT NULL,
	latest_updated_on timestamp(3) NULL,
	delete_flag bool DEFAULT false NOT NULL,
	CONSTRAINT readers_pkey PRIMARY KEY (id)
);
-- Indexes for readers
CREATE INDEX readers_reader_id_idx ON public.readers USING btree (reader_id);
CREATE INDEX readers_notification_id_idx ON public.readers USING btree (notification_id);
CREATE INDEX readers_delete_flag_idx ON public.readers USING btree (delete_flag);
-- Composite index for notification-reader queries
CREATE INDEX readers_notification_reader_idx ON public.readers USING btree (notification_id, reader_id);

-- public.regions definition

-- Drop table

-- DROP TABLE public.regions;

CREATE TABLE public.regions (
	id serial4 NOT NULL,
	region_name text NOT NULL,
	created_by_id int4 DEFAULT 0 NOT NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NULL,
	latest_updated_on timestamp(3) NULL,
	delete_flag bool DEFAULT false NOT NULL,
	CONSTRAINT regions_pkey PRIMARY KEY (id)
);
-- Indexes for regions
CREATE UNIQUE INDEX regions_region_name_key ON public.regions USING btree (region_name);
CREATE INDEX regions_delete_flag_idx ON public.regions USING btree (delete_flag);


-- public.settings definition

-- Drop table

-- DROP TABLE public.settings;

CREATE TABLE public.settings (
	id serial4 NOT NULL,
	user_id int4 NOT NULL,
	unit_per_gram numeric(65, 2) NOT NULL,
	destination_id int4 NULL,
	display_shipment_weight bool NOT NULL,
	display_actual_received bool NOT NULL,
	created_by_id int4 DEFAULT 0 NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NULL,
	latest_updated_on timestamp(3) NULL,
	delete_flag bool DEFAULT false NOT NULL,
	qr_scan_init int4 NULL,
	receipt_number int4 DEFAULT 2 NULL,
	report_type int4 DEFAULT 2 NULL,
	enable_session_timeout bool DEFAULT true NOT NULL,
	session_expirytime int4 DEFAULT 24 NOT NULL,
	price_per_kilogram _numeric DEFAULT ARRAY[]::numeric[]::numeric(65,2)[] NULL,
	include_tax_type int4 DEFAULT 1 NOT NULL,
	inventory_control_type int4 DEFAULT 2 NOT NULL,
	price_per_quantity _numeric DEFAULT ARRAY[]::numeric[]::numeric(65,2)[] NULL,
	CONSTRAINT settings_pkey PRIMARY KEY (id)
);
-- Indexes for settings
CREATE UNIQUE INDEX settings_user_id_key ON public.settings USING btree (user_id);
CREATE INDEX settings_delete_flag_idx ON public.settings USING btree (delete_flag);

-- public.sys_settings definition

-- Drop table

-- DROP TABLE public.sys_settings;

CREATE TABLE public.sys_settings (
	id serial4 NOT NULL,
	setting_name varchar(256) NOT NULL,
	setting_value varchar(256) NULL,
	created_by_id int4 DEFAULT 0 NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NOT NULL,
	latest_updated_on timestamp(3) NULL,
	delete_flag bool DEFAULT false NOT NULL,
	CONSTRAINT sys_settings_pkey PRIMARY KEY (id)
);
-- Indexes for sys_settings
CREATE UNIQUE INDEX sys_settings_setting_name_key ON public.sys_settings USING btree (setting_name);
CREATE INDEX sys_settings_delete_flag_idx ON public.sys_settings USING btree (delete_flag);


-- public.the_origins definition

-- Drop table

-- DROP TABLE public.the_origins;

CREATE TABLE public.the_origins (
	id serial4 NOT NULL,
	code text NULL,
	shipping_date timestamp(3) NULL,
	shipping_gross_weight numeric(65, 2) NULL,
	shipping_tare_weight numeric(65, 2) NULL,
	shipping_net_weight numeric(65, 2) NULL,
	shipping_quantity int4 NULL,
	arrival_gross_weight numeric(65, 2) NULL,
	arrival_tare_weight numeric(65, 2) NULL,
	arrival_net_weight numeric(65, 2) NULL,
	arrival_quantity int4 NULL,
	arrival_date timestamp(3) NULL,
	type_diff int4 NULL,
	shipping_type int4 NULL,
	reason_diff varchar(300) NULL,
	created_by_id int4 DEFAULT 0 NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NULL,
	latest_updated_on timestamp(3) NULL,
	delete_flag bool DEFAULT false NOT NULL,
	qr_code varchar(256) NULL,
	destination_enterprise_id int4 NULL,
	starting_enterprise_id int4 NULL,
	inventory_id int4 NULL,
	ingredient jsonb NULL,
	setting jsonb NULL,
	destination_user_id int4 NULL,
	starting_user_id int4 NULL,
	shipping_id_list jsonb DEFAULT '[]'::jsonb NOT NULL,
	destination_enterprise_name varchar(256) NOT NULL,
	destination_license_number varchar(256) NULL,
	destination_user_name varchar(256) NOT NULL,
	destination_user_note_1 varchar(256) NULL,
	destination_user_note_2 varchar(256) NULL,
	operating_days int4 DEFAULT 0 NULL,
	shipping_inventory_type int4 DEFAULT 0 NOT NULL,
	shipping_reason_diff varchar(300) NULL,
	shipping_type_diff int4 NULL,
	starting_enterprise_name varchar(256) NOT NULL,
	starting_license_number varchar(256) NULL,
	starting_user_name varchar(256) NOT NULL,
	starting_user_note_1 varchar(256) NULL,
	starting_user_note_2 varchar(256) NULL,
	CONSTRAINT the_origins_pkey PRIMARY KEY (id)
);
-- Indexes for the_origins
CREATE INDEX the_origins_arrival_date_idx ON public.the_origins USING btree (arrival_date);
CREATE INDEX the_origins_code_idx ON public.the_origins USING btree (code);
CREATE INDEX the_origins_destination_enterprise_id_idx ON public.the_origins USING btree (destination_enterprise_id);
CREATE INDEX the_origins_destination_user_id_idx ON public.the_origins USING btree (destination_user_id);
CREATE INDEX the_origins_inventory_id_idx ON public.the_origins USING btree (inventory_id);
CREATE INDEX the_origins_qr_code_idx ON public.the_origins USING btree (qr_code);
CREATE INDEX the_origins_shipping_date_idx ON public.the_origins USING btree (shipping_date);
CREATE INDEX the_origins_shipping_type_idx ON public.the_origins USING btree (shipping_type);
CREATE INDEX the_origins_starting_enterprise_id_idx ON public.the_origins USING btree (starting_enterprise_id);
CREATE INDEX the_origins_starting_user_id_idx ON public.the_origins USING btree (starting_user_id);
-- Additional indexes for the_origins
CREATE INDEX the_origins_delete_flag_idx ON public.the_origins USING btree (delete_flag);
-- Composite indexes for common queries
CREATE INDEX the_origins_user_date_idx ON public.the_origins USING btree (destination_user_id, arrival_date);
CREATE INDEX the_origins_enterprise_date_idx ON public.the_origins USING btree (destination_enterprise_id, arrival_date);
CREATE INDEX the_origins_shipping_arrival_date_idx ON public.the_origins USING btree (shipping_date, arrival_date);


-- public.the_origins_history definition

-- Drop table

-- DROP TABLE public.the_origins_history;

CREATE TABLE public.the_origins_history (
	id serial4 NOT NULL,
	the_origins_history_type int4 NULL,
	the_origins_id int4 DEFAULT 0 NOT NULL,
	the_origins_history_created_by_id int4 DEFAULT 0 NOT NULL,
	the_origins_history_created_on timestamp(3) NOT NULL,
	code text NOT NULL,
	shipping_date timestamp(3) NULL,
	shipping_gross_weight numeric(65, 2) NULL,
	shipping_tare_weight numeric(65, 2) NULL,
	shipping_net_weight numeric(65, 2) NULL,
	shipping_quantity int4 NULL,
	arrival_gross_weight numeric(65, 2) NULL,
	arrival_tare_weight numeric(65, 2) NULL,
	arrival_net_weight numeric(65, 2) NULL,
	arrival_quantity int4 NULL,
	arrival_date timestamp(3) NULL,
	type_diff int4 NULL,
	shipping_type int4 NULL,
	reason_diff varchar(300) NULL,
	created_by_id int4 DEFAULT 0 NOT NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NOT NULL,
	latest_updated_on timestamp(3) NULL,
	delete_flag bool DEFAULT false NOT NULL,
	qr_code varchar(256) NULL,
	destination_enterprise_id int4 NULL,
	starting_enterprise_id int4 NULL,
	inventory_id int4 NULL,
	ingredient jsonb NULL,
	setting jsonb NULL,
	destination_user_id int4 NULL,
	starting_user_id int4 NULL,
	shipping_type_diff int4 NULL,
	shipping_reason_diff varchar(300) NULL,
	operating_days int4 DEFAULT 0 NULL,
	destination_enterprise_name varchar(256) NOT NULL,
	destination_user_name varchar(256) NOT NULL,
	destination_license_number varchar(256) NULL,
	destination_user_note_1 varchar(256) NULL,
	destination_user_note_2 varchar(256) NULL,
	starting_enterprise_name varchar(256) NOT NULL,
	starting_user_name varchar(256) NOT NULL,
	starting_license_number varchar(256) NULL,
	starting_user_note_1 varchar(256) NULL,
	starting_user_note_2 varchar(256) NULL,
	shipping_inventory_type int4 DEFAULT 0 NOT NULL,
	shipping_id_list jsonb DEFAULT '[]'::jsonb NOT NULL,
	CONSTRAINT the_origins_history_pkey PRIMARY KEY (id)
);
-- Indexes for the_origins_history
CREATE INDEX the_origins_history_created_by_id_idx ON public.the_origins_history USING btree (created_by_id);
CREATE INDEX the_origins_history_destination_enterprise_id_idx ON public.the_origins_history USING btree (destination_enterprise_id);
CREATE INDEX the_origins_history_destination_user_id_idx ON public.the_origins_history USING btree (destination_user_id);
CREATE INDEX the_origins_history_inventory_id_idx ON public.the_origins_history USING btree (inventory_id);
CREATE INDEX the_origins_history_latest_updated_by_id_idx ON public.the_origins_history USING btree (latest_updated_by_id);
CREATE INDEX the_origins_history_starting_enterprise_id_idx ON public.the_origins_history USING btree (starting_enterprise_id);
CREATE INDEX the_origins_history_starting_user_id_idx ON public.the_origins_history USING btree (starting_user_id);
CREATE INDEX the_origins_history_the_origins_history_created_by_id_idx ON public.the_origins_history USING btree (the_origins_history_created_by_id);
CREATE INDEX the_origins_history_the_origins_id_idx ON public.the_origins_history USING btree (the_origins_id);
-- Additional indexes for the_origins_history
CREATE INDEX the_origins_history_type_idx ON public.the_origins_history USING btree (the_origins_history_type);
CREATE INDEX the_origins_history_created_on_idx ON public.the_origins_history USING btree (the_origins_history_created_on);
CREATE INDEX the_origins_history_code_idx ON public.the_origins_history USING btree (code);
CREATE INDEX the_origins_history_arrival_date_idx ON public.the_origins_history USING btree (arrival_date);
CREATE INDEX the_origins_history_shipping_date_idx ON public.the_origins_history USING btree (shipping_date);
CREATE INDEX the_origins_history_delete_flag_idx ON public.the_origins_history USING btree (delete_flag);
CREATE INDEX the_origins_history_shipping_type_idx ON public.the_origins_history USING btree (shipping_type);
CREATE INDEX the_origins_history_type_diff_idx ON public.the_origins_history USING btree (type_diff);


-- public.users definition

-- Drop table

-- DROP TABLE public.users;

CREATE TABLE public.users (
	id serial4 NOT NULL,
	user_code varchar(256) NULL,
	qr_code varchar(256) NULL,
	enterprise_id int4 NULL,
	"name" varchar(256) NOT NULL,
	name_kana varchar(256) NULL,
	phone varchar(256) NOT NULL,
	"password" text NOT NULL,
	"role" varchar(2) NOT NULL,
	created_by_id int4 DEFAULT 0 NULL,
	latest_updated_by_id int4 NULL,
	created_on timestamp(3) NULL,
	latest_updated_on timestamp(3) NULL,
	delete_flag bool DEFAULT false NOT NULL,
	statistics_date_from timestamp(3) NULL,
	license_id int4 NULL,
	note_1 varchar(256) NULL,
	note_2 varchar(256) NULL,
	license_number varchar(256) NULL,
	name_nospace varchar(256) NOT NULL,
	name_kana_nospace varchar(256) NULL,
	status int4 NULL,
	province_id int4 NULL,
	province_custom_data json NULL,
	locktime timestamp(3) NULL,
	loginfail int4 DEFAULT 0 NOT NULL,
	account_registration_status int4 DEFAULT 0 NOT NULL,
	enterprise_type int4 NULL,
	staff_type int4 NULL,
	enable_export_function bool DEFAULT false NOT NULL,
	CONSTRAINT users_pkey PRIMARY KEY (id)
);
-- Indexes for users
CREATE INDEX users_enterprise_id_idx ON public.users USING btree (enterprise_id);
CREATE INDEX users_license_id_idx ON public.users USING btree (license_id);
CREATE INDEX users_name_kana_nospace_idx ON public.users USING btree (name_kana_nospace);
CREATE INDEX users_name_nospace_idx ON public.users USING btree (name_nospace);
CREATE INDEX users_phone_idx ON public.users USING btree (phone);
CREATE INDEX users_status_idx ON public.users USING btree (status);
CREATE UNIQUE INDEX users_user_code_key ON public.users USING btree (user_code);
-- Additional indexes for users
CREATE INDEX users_qr_code_idx ON public.users USING btree (qr_code);
CREATE INDEX users_name_idx ON public.users USING btree (name);
CREATE INDEX users_name_kana_idx ON public.users USING btree (name_kana);
CREATE INDEX users_role_idx ON public.users USING btree (role);
CREATE INDEX users_delete_flag_idx ON public.users USING btree (delete_flag);
CREATE INDEX users_created_by_id_idx ON public.users USING btree (created_by_id);
CREATE INDEX users_created_on_idx ON public.users USING btree (created_on);
CREATE INDEX users_province_id_idx ON public.users USING btree (province_id);
CREATE INDEX users_locktime_idx ON public.users USING btree (locktime);
CREATE INDEX users_loginfail_idx ON public.users USING btree (loginfail);
CREATE INDEX users_account_registration_status_idx ON public.users USING btree (account_registration_status);
CREATE INDEX users_enterprise_type_idx ON public.users USING btree (enterprise_type);
CREATE INDEX users_staff_type_idx ON public.users USING btree (staff_type);
CREATE INDEX users_license_number_idx ON public.users USING btree (license_number);
-- Composite indexes for common queries
CREATE INDEX users_enterprise_role_idx ON public.users USING btree (enterprise_id, role);
CREATE INDEX users_status_delete_flag_idx ON public.users USING btree (status, delete_flag);