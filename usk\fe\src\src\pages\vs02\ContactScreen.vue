<template>
  <div>
    <q-card class="tw:px-6 tw:text-[#333333] tw:tl:mt-0 tw:mt-[1rem] tw:pb-[12rem] tw:tl:pb-[6rem]">
      <div class="tw:text-l-design tw:font-bold tw:text-center tw:tl:text-start">お問い合わせ</div>
      <div
      class="tw:text-m-design tw:font-normal tw:my-4"
      >パスワードを忘れた場合は下記の連絡先までお電話の上再発行のご依頼をお願いいたします。</div>
      <div class="tw:grid tw:tl:grid-cols-9 tw:grid-cols-1 tw:gap-4 tw:my-8">
        <div class="tw:text-l-design tw:font-bold tw:text-center tw:tl:text-right tw:tl:col-span-3">一般社団法人</div>
        <div class="tw:text-l-design tw:font-bold tw:text-center tw:tl:col-span-3">全日本持続的養鰻機構</div>
        <div class="tw:text-l-design tw:font-bold tw:flex tw:justify-center tw:tl:justify-start tw:tl:col-span-3">
          <div class="tw:flex tw:my-6 tw:tl:my-0">
            <div class="tw:font-normal tw:mr-1">Tel.</div>
            <a
            href="tel:+81357977690"
            class="tw:text-[#004AB9] tw:tl:text-[#333333]">03-5797-7690</a>
          </div>
        </div>
      </div>
      <div class="tw:text-m-design tw:font-bold tw:text-center tw:mt-4">パスワード再発行に必要な情報</div>

      <div class="tw:flex tw:justify-center tw:mt-4 tw:w-full tw:pb-6">
        <div class="tw:border tw:border-[#D2D2D2] tw:rounded-none tw:w-[55.438rem]">
          <div class="tw:flex tw:flex-col tw:divide-y tw:divide-[#D2D2D2]">
            <div class="tw:flex tw:flex-row tw:divide-x tw:divide-[#cbccd2]">
              <span
                class="tw:w-[50%] tw:bg-[#E2E3EA] tw:pl-5 tw:dt:pt-1 tw:font-bold tw:text-s-design tw:items-center tw:flex tw:p-3">
                項目
              </span>
              <div class="tw:w-[50%] tw:pl-5 tw:dt:pt-1 tw:font-bold tw:text-s-design tw:p-3 tw:bg-[#E2E3EA]">
                例
              </div>
            </div>

            <div class="tw:flex tw:flex-row tw:divide-x tw:divide-[#cbccd2]">
              <span class="tw:w-[50%] tw:pl-5 tw:dt:pt-1 tw:font-bold tw:text-s-design tw:items-center tw:flex tw:p-3">
                ユーザーID
              </span>
              <div class="tw:w-[50%] tw:pl-5 tw:dt:pt-1 tw:font-normal tw:text-s-design tw:p-3">
                0123456-1234
              </div>
            </div>

            <div class="tw:flex tw:flex-row tw:divide-x tw:divide-[#cbccd2]">
              <span
                class="tw:w-[50%] tw:pl-5 tw:dt:pt-1 tw:font-bold tw:text-s-design tw:items-center tw:flex tw:p-3 tw:bg-[#F7F7F9]">
                届出事業者名/氏名
              </span>
              <div class="tw:w-[50%] tw:pl-5 tw:dt:pt-1 tw:font-normal tw:text-s-design tw:p-3 tw:bg-[#F7F7F9]">
                養鰻機構/鰻 食太
              </div>
            </div>

            <div class="tw:flex tw:flex-row tw:divide-x tw:divide-[#cbccd2]">
              <span class="tw:w-[50%] tw:pl-5 tw:dt:pt-1 tw:font-bold tw:text-s-design tw:items-center tw:flex tw:p-3">
                お住いの都道府県
              </span>
              <div class="tw:w-[50%] tw:pl-5 tw:dt:pt-1 tw:font-normal tw:text-s-design tw:p-3">
                東京都
              </div>
            </div>

            <div class="tw:flex tw:flex-row tw:divide-x tw:divide-[#cbccd2]">
              <span
                class="tw:w-[50%] tw:pl-5 tw:dt:pt-1 tw:font-bold tw:text-s-design tw:items-center tw:flex tw:p-3 tw:bg-[#F7F7F9]">
                届出番号/許可番号
              </span>
              <div class="tw:w-[50%] tw:pl-5 tw:dt:pt-1 tw:font-normal tw:text-s-design tw:p-3 tw:bg-[#F7F7F9]">
                0123456/稚う第11-22-3号
              </div>
            </div>

          </div>
        </div>
      </div>
    </q-card>

    <!-- Footer Button -->
    <q-footer elevated class="tw:bg-white tw:p-4
    tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
    tw:tl:justify-start tw:tl:items-center tw:flex tw:justify-center tw:mt-4 tw:flex-col tw:gap-8 tw:tl:flex-row">
      <div class="tw:flex tw:gap-4 tw:tl:gap-8">
        <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-[#004AB9] tw:text-m-design tw:tl:font-bold
        tw:tl:w-[22rem] tw:h-[4.75rem] tw:min-h-[4.75rem] tw:max-h-[4.75rem]
        tw:w-[95.5%]`"
        label="ログイン画面に戻る"
        @click.prevent="goLoginUserId"
      />
      </div>
    </q-footer>
  </div>
</template>

<script setup>
import BaseButton from 'components/base/vs/BaseButton.vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// Methods
const goLoginUserId = () => {
  router.push({ name: 'loginUserId' });
};
</script>
