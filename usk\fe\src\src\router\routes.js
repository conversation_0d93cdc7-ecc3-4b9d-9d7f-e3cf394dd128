import { ENTERPRISE_TYPE_ENUM, ROLES_ENUM, STAFF_TYPE_ENUM, ROLES_ENUM_OPTIONS_VALUES } from 'src/helpers/constants';

const routes = [
  // #region business website routes
  {
    path: '',
    component: () => import('layouts/PageLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('pages/vs17/IndexPage.vue'),
        name: 'home',
        meta: {
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'setting',
        component: () => import('pages/vs16/IndexPage.vue'),
        name: 'setting',
        meta: {
          title: '設定',
          routeBack: 'home',
          isBreadcrumb: true,
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'setting-confirm',
        component: () => import('pages/vs16/SettingConfirm.vue'),
        name: 'settingConfirm',
        meta: {
          routeBack: 'home',
          preTitle: '設定',
          isBreadcrumb: true,
          preRoute: 'setting',
          tag: '設定確認',
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'manual-registration',
        name: 'manualRegistration',
        component: () => import('pages/vs10/ManualRegistration.vue'),
        meta: {
          title: '入荷登録',
          tag: '入荷登録入力',
          routeBack: 'home',
          isBreadcrumb: true,
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'manual-registration-confirm',
        name: 'manualRegistrationConfirm',
        component: () => import('pages/vs10/ManualRegistrationConfirm.vue'),
        meta: {
          title: '入荷登録',
          tag: '入荷登録確認',
          isBreadcrumb: true,
          prevRoute: 'manualRegistration',
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'shipping-list',
        name: 'shippingList',
        component: () => import('pages/vs09/ShippingRecordList.vue'),
        meta: {
          title: '出荷実績管理',
          isBreadcrumb: true,
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
        },
      },
      {
        path: 'shipping-detail/:id',
        name: 'shippingDetail',
        component: () => import('pages/vs09/ShippingDetail.vue'),
        meta: {
          title: '出荷実績管理',
          isBreadcrumb: true,
          mainRoute: 'shippingList',
          routeBack: 'shippingList',
          tag: '出荷実績詳細',
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
        },
      },
      {
        path: 'shipping-edit/:id',
        name: 'shippingEdit',
        component: () => import('pages/vs09/ShippingEdit.vue'),
        meta: {
          title: '出荷実績管理',
          mainRoute: 'shippingList',
          routeBack: 'shippingDetail',
          isBreadcrumb: true,
          tag: '出荷実績詳細',
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
        },
      },
      {
        path: 'arrival-list',
        name: 'arrivalList',
        component: () => import('pages/vs11/ArrivalRecordList.vue'),
        meta: {
          title: '入荷実績管理',
          routeBack: 'home',
          auth: true,
          isBreadcrumb: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'arrival-detail/:id',
        name: 'arrivalDetail',
        component: () => import('pages/vs11/ArrivalDetail.vue'),
        meta: {
          title: '入荷実績管理',
          routeBack: 'arrivalList',
          mainRoute: 'arrivalList',
          tag: '入荷実績詳細',
          auth: true,
          isBreadcrumb: true,
          role: [
            ROLES_ENUM.NORMAL_USER, ROLES_ENUM_OPTIONS_VALUES.CATCH_ENTERPRISE, ROLES_ENUM_OPTIONS_VALUES.CATCH_STAFF,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'arrival-edit/:id',
        name: 'arrivalEdit',
        component: () => import('pages/vs11/ArrivalEdit.vue'),
        meta: {
          title: '入荷実績管理',
          mainRoute: 'arrivalList',
          routeBack: 'arrivalDetail',
          auth: true,
          tag: '入荷実績詳細',
          isBreadcrumb: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'inventory-list',
        name: 'inventoryList',
        component: () => import('pages/vs06/InventoryList.vue'),
        meta: {
          title: '在庫管理',
          isBreadcrumb: true,
          routeBack: 'home',
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'inventory-detail/:id',
        name: 'inventoryDetail',
        component: () => import('pages/vs06/InventoryDetail.vue'),
        meta: {
          title: '在庫管理',
          tag: '在庫管理詳細',
          mainRoute: 'inventoryList',
          routeBack: 'inventoryList',
          isBreadcrumb: true,
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'inventory-edit/:id',
        name: 'inventoryEdit',
        component: () => import('pages/vs06/InventoryEdit.vue'),
        meta: {
          title: '在庫管理',
          auth: true,
          isBreadcrumb: true,
          mainRoute: 'inventoryList',
          tag: '在庫管理詳細',
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'inventory-edit-list/',
        name: 'inventoryEditedList',
        component: () => import('pages/vs06/InventoryEditedList.vue'),
        meta: {
          title: '在庫修正実績',
          preTitle: '在庫管理',
          preRoute: 'inventoryList',
          routeBack: 'inventoryList',
          auth: true,
          isBreadcrumb: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'inventory-edited-detail/:id',
        name: 'inventoryEditedDetail',
        component: () => import('pages/vs06/InventoryEditedDetail.vue'),
        meta: {
          title: '在庫修正実績',
          mainRoute: 'inventoryEditedList',
          tag: '在庫修正実績詳細',
          isBreadcrumb: true,
          routeBack: 'inventoryEditedList',
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'employee-list',
        name: 'employeeList',
        component: () => import('pages/vs05/EmployeeList.vue'),
        meta: {
          title: '従事者管理',
          isBreadcrumb: true,
          routeBack: 'home',
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
          staff: [STAFF_TYPE_ENUM.ENTERPRISE],
        },
      },
      {
        path: 'employee-detail/:id',
        name: 'employeeDetail',
        component: () => import('pages/vs05/EmployeeDetail.vue'),
        meta: {
          title: '従事者管理',
          tag: '従事者詳細',
          isBreadcrumb: true,
          mainRoute: 'employeeList',
          routeBack: 'employeeList',
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
          staff: [STAFF_TYPE_ENUM.ENTERPRISE],
        },
      },
      {
        path: 'employee-edit/:id',
        name: 'employeeEdit',
        component: () => import('pages/vs05/EmployeeEdit.vue'),
        meta: {
          title: '従事者詳細',
          routeBack: 'employeeDetail',
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
          staff: [STAFF_TYPE_ENUM.ENTERPRISE],
        },
      },
      {
        path: 'profile',
        component: () => import('pages/vs15/IndexPage.vue'),
        name: 'profile',
        meta: {
          title: 'ユーザー設定',
          routeBack: 'home',
          isBreadcrumb: true,
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER, ROLES_ENUM.LOCAL_GOVERNMENT_ADMIN,
            ROLES_ENUM.FISHERIES_DEPARTMENT_ADMIN,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'manual',
        component: () => import('pages/vs14/ManualPage.vue'),
        name: 'manual',
        meta: {
          title: 'マニュアル',
          routeBack: 'home',
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'license',
        component: () => import('pages/vs14/LicensePage.vue'),
        name: 'license',
        meta: {
          title: 'ライセンス',
          routeBack: 'home',
          isBreadcrumb: true,
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER, ROLES_ENUM.LOCAL_GOVERNMENT_ADMIN,
            ROLES_ENUM.FISHERIES_DEPARTMENT_ADMIN,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'notification-list',
        component: () => import('pages/vs13/NotificationList.vue'),
        name: 'notificationList',
        meta: {
          auth: true,
          title: 'お知らせ一覧',
          isBreadcrumb: true,
          role: [
            ROLES_ENUM.NORMAL_USER, ROLES_ENUM.LOCAL_GOVERNMENT_ADMIN,
            ROLES_ENUM.FISHERIES_DEPARTMENT_ADMIN,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'notification/:id',
        component: () => import('pages/vs13/NotificationDetail.vue'),
        name: 'notificationDetail',
        meta: {
          auth: true,
          title: 'お知らせ一覧',
          tag: 'お知らせ詳細',
          mainRoute: 'notificationList',
          isBreadcrumb: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'partner',
        name: 'partner',
        component: () => import('src/pages/vs04/ListPage.vue'),
        meta: {
          title: '取引先管理',
          routeBack: 'home',
          isBreadcrumb: true,
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'partner-hybrid',
        name: 'partnerHybrid',
        component: () => import('src/pages/vs04/HybridPage.vue'),
        meta: {
          preTitle: '取引先管理',
          title: '取引先登録',
          routeBack: 'partner',
          preRoute: 'partner',
          isBreadcrumb: true,
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'partner-hybrid-detail/:id',
        name: 'partnerHybridDetail',
        component: () => import('pages/vs04/PartnerHybridDetail.vue'),
        meta: {
          title: '取引先登録',
          preTitle: '取引先管理',
          tag: '取引先詳細',
          routeBack: 'partnerHybrid',
          mainRoute: 'partnerHybrid',
          preRoute: 'partner',
          isBreadcrumb: true,
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'partner-hybrid-detail-confirm/:id',
        name: 'partnerHybridDetailConfirm',
        component: () => import('pages/vs04/PartnerHybridDetailConfirm.vue'),
        meta: {
          title: '取引先登録',
          preTitle: '取引先管理',
          tag: '取引先詳細',
          routeBack: 'partner-hybrid-detail',
          mainRoute: 'partnerHybrid',
          preRoute: 'partnerHybridDetail',
          isBreadcrumb: true,
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'partner-input',
        name: 'partnerInput',
        component: () => import('pages/vs04/InputPage.vue'),
        meta: {
          preTitle: '取引先管理',
          title: '取引先登録',
          tag: '取引先新規登録',
          routeBack: 'partnerHybrid',
          mainRoute: 'partnerHybrid',
          preRoute: 'partner',
          isBreadcrumb: true,
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'partner-input-confirm',
        name: 'partnerConfirm',
        component: () => import('pages/vs04/PartnerConfirm.vue'),
        meta: {
          title: '取引先登録',
          preTitle: '取引先管理',
          routeBack: 'partnerHybrid',
          preRoute: 'partner',
          mainRoute: 'partnerHybrid',
          tag: '取引先新規登録確認',
          isBreadcrumb: true,
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'partner-detail/:id',
        name: 'partnerDetail',
        component: () => import('pages/vs04/PartnerDetail.vue'),
        meta: {
          title: '取引先管理',
          routeBack: 'partner',
          mainRoute: 'partner',
          isBreadcrumb: true,
          tag: '取引先詳細',
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'register-proxy-outbound-shipment',
        name: 'registerProxyOutboundShipment',
        component: () => import('pages/vs08/ProxyShipmentOutboundRegistration.vue'),
        meta: {
          auth: true,
          prevRoute: ['arrivalQrCamera', 'arrivalQrScan', 'confirmProxyOutboundShipment'],
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE],
        },
      },
      {
        path: 'confirm-proxy-outbound-shipment',
        name: 'confirmProxyOutboundShipment',
        component: () => import('pages/vs08/ProxyShipmentOutboundConfirm.vue'),
        meta: {
          auth: true,
          prevRoute: ['registerProxyOutboundShipment'],
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE],
        },
      },
      {
        path: 'register-outbound-shipment',
        name: 'registerOutboundShipment',
        component: () => import('pages/vs07/ShipmentOutboundRegistration.vue'),
        meta: {
          title: '出荷登録',
          tag: '出荷登録入力',
          isBreadcrumb: true,
          prevRoute: ['registerOutboundConfirm', 'home'],
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
        },
      },
      {
        path: 'register-outbound-confirm',
        name: 'registerOutboundConfirm',
        component: () => import('src/pages/vs07/ShipmentOutboundConfirm.vue'),
        meta: {
          title: '出荷登録',
          tag: '出荷登録確認',
          isBreadcrumb: true,
          prevRoute: ['registerOutboundShipment'],
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
        },
      },
      {
        path: 'kagoshima',
        name: 'authorizerInformation',
        component: () => import('pages/kagoshima/ds02/AuthorizerInformation.vue'),
        meta: {
          title: '許可者情報',
          routeBack: 'home',
          auth: false,
          role: [],
        },
      },
      {
        path: 'shipping/:qrCode',
        name: 'arrivalRegistration',
        component: () => import('pages/vs10/ArrivalRegistration.vue'),
        meta: {
          preTitle: '入荷登録',
          tag: '入荷登録確認',
          isBreadcrumb: true,
          preRoute: 'arrivalQrCamera',
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'arrival-qr-camera',
        name: 'arrivalQrCamera',
        component: () => import('pages/vs10/ArrivalQrCamera.vue'),
        meta: {
          preTitle: '入荷登録',
          tag: 'カメラで入荷登録',
          preRoute: 'home',
          isBreadcrumb: true,
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'arrival-qr-scan',
        name: 'arrivalQrScan',
        component: () => import('pages/vs10/ArrivalQrScan.vue'),
        meta: {
          preTitle: '入荷登録',
          tag: 'QRスキャナーで入荷登録',
          preRoute: 'home',
          isBreadcrumb: true,
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'shipping/registration-confirmation/:qrCode',
        name: 'registrationConfirmation',
        component: () => import('src/pages/vs10/ArrivalRegistrationConfirmation.vue'),
        meta: {
          preTitle: '入荷登録',
          preRoute: 'arrivalRegistration',
          tag: '入荷登録確認',
          isBreadcrumb: true,
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'local-government-admin',
        name: 'localGovernmentAdmin',
        component: () => import('pages/fileDummy/LocalGovernmentAdmin.vue'),
        meta: {
          title: 'Local Government Site Admin',
          routeBack: 'home',
          isBreadcrumb: true,
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER, ROLES_ENUM.LOCAL_GOVERNMENT_ADMIN,
            ROLES_ENUM.FISHERIES_DEPARTMENT_ADMIN,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'privacy-policy',
        component: () => import('pages/vs14/PrivacyPolicyDocxViewer.vue'),
        name: 'privacyPolicy',
        meta: {
          title: 'プライバシーポリシー',
          routeBack: 'home',
          isBreadcrumb: true,
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER, ROLES_ENUM.LOCAL_GOVERNMENT_ADMIN,
            ROLES_ENUM.FISHERIES_DEPARTMENT_ADMIN,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'term-of-use',
        component: () => import('pages/vs14/TermsOfUseDocxViewer.vue'),
        name: 'termOfUse',
        meta: {
          title: '利用規約',
          routeBack: 'home',
          isBreadcrumb: true,
          auth: true,
          role: [
            ROLES_ENUM.NORMAL_USER, ROLES_ENUM.LOCAL_GOVERNMENT_ADMIN,
            ROLES_ENUM.FISHERIES_DEPARTMENT_ADMIN,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },

    ],
  },
  {
    path: '',
    component: () => import('layouts/LoginLayout.vue'),
    children: [
      {
        path: 'login/user-id',
        name: 'loginUserId',
        component: () => import('pages/vs02/LoginUserId.vue'),
        meta: {
          auth: false,
        },
      },

      {
        path: 'login/:qrCode',
        name: 'loginLink',
        component: () => import('pages/vs02/LoginLink.vue'),
        meta: {
          auth: false,
        },
      },
      {
        path: 'login/qr-scan',
        name: 'loginQrScan',
        component: () => import('pages/vs02/LoginQrScan.vue'),
        meta: {
          auth: false,
        },
      },
      {
        path: 'login/contact',
        name: 'contactScreen',
        component: () => import('pages/vs02/ContactScreen.vue'),
        meta: {
          auth: false,
        },
      },
      {
        path: 'privacy-policy-user',
        component: () => import('pages/vs14/PrivacyPolicyDocxViewer.vue'),
        name: 'privacyPolicyUser',
        meta: {
          title: '',
          role: [],
        },
      },
      {
        path: 'recovery-password',
        component: () => import('pages/vs02/RecoveryPassword.vue'),
        name: 'recoveryPassword',
        meta: {
          title: '',
          role: [],
          auth: true,
        },
      },
      {
        path: 'license-public',
        component: () => import('pages/vs14/LicensePage.vue'),
        name: 'licenseVS',
        meta: {
          title: 'ライセンス',
          isBreadcrumb: true,
          routeBack: 'home',
          role: [
            ROLES_ENUM.NORMAL_USER,
          ],
          type: [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE, ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
        },
      },
      {
        path: 'policy-public',
        name: 'privacyPolicyVS',
        component: () => import('pages/vs14/PrivacyPolicyDocxViewer.vue'),
        meta: {
          title: 'プライバシーポリシー',
          isBreadcrumb: true,
          routeBack: 'adminUserManager',
          role: [ROLES_ENUM.ADMIN, ROLES_ENUM.SYSTEM_ADMIN],
        },
      },
      {
        path: 'term-of-use-public',
        name: 'termsOfUserVS',
        component: () => import('pages/vs14/TermsOfUseDocxViewer.vue'),
        meta: {
          title: '利用規約',
          isBreadcrumb: true,
          routeBack: 'adminUserManager',
          role: [ROLES_ENUM.ADMIN, ROLES_ENUM.SYSTEM_ADMIN],
        },
      },
    ],
  },
  // #endregion business website routes
  // #region admin site routes
  // #region routes using: LoginAdminLayout
  {
    path: '',
    component: () => import('layouts/admin/LoginAdminLayout.vue'),
    children: [
      {
        path: 'admin/login',
        name: 'adminLogin',
        component: () => import('pages/admin/as08/LoginAdmin.vue'),
        meta: {
          auth: false,
        },
      },
    ],
  },
  // #endregion routes using: LoginAdminLayout
  // #region routes using: AdminLayout
  {
    path: '',
    component: () => import('layouts/admin/AdminLayout.vue'),
    children: [
      // #region notification routes
      {
        path: '/admin/notify',
        component: () => import('pages/admin/as07/NotificationList.vue'),
        name: 'adminNotificationManager',
        meta: {
          title: 'お知らせ管理',
          auth: true,
          role: [ROLES_ENUM.ADMIN, ROLES_ENUM.SYSTEM_ADMIN],
        },
      },
      {
        path: '/admin/notification/detail/:id',
        name: 'adminNotificationDetail',
        component: () => import('pages/admin/as07/NotificationDetail.vue'),
        meta: {
          auth: true,
          title: 'お知らせ管理',
          role: [ROLES_ENUM.ADMIN, ROLES_ENUM.SYSTEM_ADMIN],
        },
      },
      {
        path: '/admin/notification/register',
        name: 'adminNotificationRegister',
        component: () => import('pages/admin/as07/NotificationRegister.vue'),
        meta: {
          auth: true,
          title: 'お知らせ管理',
          role: [ROLES_ENUM.ADMIN, ROLES_ENUM.SYSTEM_ADMIN],
        },
      },
      {
        path: '/admin',
        component: () => import('pages/admin/as05/UserList.vue'),
        name: 'adminUserManager',
        meta: {
          auth: true,
          role: [ROLES_ENUM.ADMIN, ROLES_ENUM.SYSTEM_ADMIN],
          title: 'ユーザー管理',
        },
      },
      {
        path: '/admin/user/:id',
        component: () => import('pages/admin/as05/DetailUser.vue'),
        name: 'adminUserDetail',
        meta: {
          auth: true,
          role: [ROLES_ENUM.ADMIN, ROLES_ENUM.SYSTEM_ADMIN],
          title: 'ユーザー情報詳細',
        },
      },
      {
        path: '/admin/user-edit/:id',
        component: () => import('pages/admin/as05/EditUser.vue'),
        name: 'adminUserEdit',
        meta: {
          auth: true,
          role: [ROLES_ENUM.ADMIN, ROLES_ENUM.SYSTEM_ADMIN],
          title: 'ユーザー情報詳細',
        },
      },
      {
        path: '/admin/user-edit-complete/:id',
        component: () => import('pages/admin/as05/CompleteEditUser.vue'),
        name: 'adminUserEditComplete',
        meta: {
          auth: true,
          role: [ROLES_ENUM.ADMIN, ROLES_ENUM.SYSTEM_ADMIN],
          title: 'ユーザー情報詳細',
          prevRoute: ['adminUserEdit'],
        },
      },
      {
        path: '/admin/user-register',
        component: () => import('pages/admin/as05/RegisterNewUser.vue'),
        name: 'adminUserRegister',
        meta: {
          auth: true,
          role: [ROLES_ENUM.ADMIN, ROLES_ENUM.SYSTEM_ADMIN],
          title: '新規ユーザー登録',
        },
      },
      {
        path: '/admin/user-register-complete',
        component: () => import('pages/admin/as05/CompleteNewUser.vue'),
        name: 'adminUserRegisterComplete',
        meta: {
          auth: true,
          role: [ROLES_ENUM.ADMIN, ROLES_ENUM.SYSTEM_ADMIN],
          title: '新規ユーザー登録',
          prevRoute: ['adminUserRegister'],
        },
      },
      {
        path: '/admin/user-import',
        component: () => import('pages/admin/as05/ImportCSVUser.vue'),
        name: 'adminUserImport',
        meta: {
          auth: true,
          role: [ROLES_ENUM.ADMIN, ROLES_ENUM.SYSTEM_ADMIN],
          title: 'ユーザー設定CSV取込',
        },
      },
      // #endregion user management routes
      // #region system settings routes
      {
        path: '/admin/settings',
        component: () => import('pages/admin/as02/AdminSystemSettings.vue'),
        name: 'adminSystemSettings',
        meta: {
          auth: true,
          role: [ROLES_ENUM.ADMIN, ROLES_ENUM.SYSTEM_ADMIN],
          title: '各種設定値管理',
        },
      },
      {
        path: '/admin/batch-non-active-update',
        component: () => import('pages/admin/as05/BatchNonActiveUpdate.vue'),
        name: 'adminBatchNonActiveUpdate',
        meta: {
          auth: true,
          role: [ROLES_ENUM.ADMIN, ROLES_ENUM.SYSTEM_ADMIN],
          title: '一括「無許可」化',
        },
      },
      // #endregion system settings routes
    ],
  },
  {

    path: '',
    component: () => import('layouts/admin/AdminLayoutIFrame.vue'),
    children: [
      // #endregion notification routes
      // #region document routes
      {
        path: '/admin/policy',
        name: 'privacyPolicyAdmin',
        component: () => import('pages/admin/as09/PrivacyPolicyDocxViewer.vue'),
        meta: {
          title: '',
          auth: true,
          routeBack: 'adminUserManager',
          role: [ROLES_ENUM.ADMIN, ROLES_ENUM.SYSTEM_ADMIN],
        },
      },
      {
        path: '/admin/term-of-use',
        name: 'termsOfUserAdmin',
        component: () => import('pages/admin/as09/TermsOfUseDocxViewer.vue'),
        meta: {
          title: '',
          auth: true,
          routeBack: 'adminUserManager',
          role: [ROLES_ENUM.ADMIN, ROLES_ENUM.SYSTEM_ADMIN],
        },
      },
      {
        path: '/admin/license',
        component: () => import('pages/admin/as09/LicensePage.vue'),
        name: 'licenseAdmin',
        meta: {
          title: 'ライセンス',
          routeBack: 'adminUserManager',
          auth: true,
          role: [
            ROLES_ENUM.ADMIN, ROLES_ENUM.SYSTEM_ADMIN,
          ],
        },
      },
    ],
  },
  {
    path: '',
    component: () => import('layouts/fa/FAPageLayout.vue'),
    children: [
      {
        path: '/fa-admin',
        name: 'fisheriesDepartmentAdmin',
        component: () => import('pages/fa03/ExportShipmentList.vue'),
        meta: {
          title: '',
          auth: true,
          role: [
            ROLES_ENUM.FISHERIES_DEPARTMENT_ADMIN,
          ],
        },
      },
      {
        path: '/fa-admin/policy',
        component: () => import('pages/fa02/PrivacyPolicyDocxViewer.vue'),
        name: 'privacyPolicyFA',
        meta: {
          title: 'プライバシーポリシー',
          isBreadcrumb: true,
          routeBack: 'adminUserManager',
          auth: true,
          role: [
            ROLES_ENUM.FISHERIES_DEPARTMENT_ADMIN,
          ],
        },
      },
      {
        path: '/fa-admin/term-of-use',
        component: () => import('pages/fa02/TermsOfUseDocxViewer.vue'),
        name: 'termsOfUserFA',
        meta: {
          title: '利用規約',
          isBreadcrumb: true,
          routeBack: 'adminUserManager',
          auth: true,
          role: [
            ROLES_ENUM.FISHERIES_DEPARTMENT_ADMIN,
          ],
        },
      },
      {
        path: '/fa-admin/license',
        component: () => import('pages/fa02/LicensePage.vue'),
        name: 'licenseFA',
        meta: {
          title: 'ライセンス',
          isBreadcrumb: true,
          routeBack: 'adminUserManager',
          auth: true,
          role: [
            ROLES_ENUM.FISHERIES_DEPARTMENT_ADMIN,
          ],
        },
      },
    ],
  },
  // #endregion admin site routes
  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
];

export default routes;
