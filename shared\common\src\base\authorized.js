const Env = require('../boot/env');
const { APP_LOGGER } = require('../boot/logger');
const { MESSAGE } = require('../utils/message');
const { UnauthorizedAccessException, InvalidPermissionException } = require('./errors');
const { ROLES_ENUM, USER_STATUS_ENUM } = require('../helpers/enum');
const auth = async (request, reply) => {
  try {
    await request.jwtVerify();
    const baseService = request.diScope.resolve('baseService');
    const connect = baseService.DB.READ;
    const user = await connect.users.findFirst({
      where: {
        id: request.user.id,
        status: {
          in: [USER_STATUS_ENUM.ACTIVE, USER_STATUS_ENUM.NONACTIVE],
        },
      },
      select: {
        id: true,
        user_code: true,
        enterprise_id: true,
        phone: true,
        role: true,
        license_id: true,
        name: true,
        name_kana: true,
        status: true,
        loginfail: true,
        locktime: true,
        account_registration_status: true,
        license: {
          select: {
            id: true,
            expiry_date: true,
          },
        },
        setting: {
          select: {
            enable_session_timeout: true,
            session_expirytime: true,
          },
        },
        enterprise_type: true,
        staff_type: true,
      },
    });
    if (!user) {
      throw new UnauthorizedAccessException(
        MESSAGE.MSG_LIMITS_PREMISSION_ERROR
      );
    }

    const setting = user.setting;
    delete user.setting;
    request.user = user;

    // create new token to reset time auto logout
    const newToken = baseService.FASTIFY.jwt.sign(
      { ...user, datetime: Date.now() },
      {
        expiresIn: setting?.enable_session_timeout
          ? `${setting.session_expirytime}h`
          : Env.JWT_EXPIRES_IN,
      }
    );
    reply.header('X-Access-Token', newToken);
  } catch {
    APP_LOGGER.request(request);
    throw new UnauthorizedAccessException(MESSAGE.AUTH_TOKEN_ERROR);
  }
}

const apiKeyVerify = (request, reply, done) => {
  const xApiKey = request.headers['x-api-key'];
  if (!xApiKey
    || xApiKey !== Env.X_API_KEY) {
    throw new UnauthorizedAccessException(MESSAGE.API_KEY_NOT_VALID);
  }
  done();
}

const authAdmin = async (request) => {
  const user = request.user;
  const roleType = user.role;
  if (roleType !== ROLES_ENUM.ADMIN && roleType !== ROLES_ENUM.SYSTEM_ADMIN) {
    throw new UnauthorizedAccessException(MESSAGE.MSG_ACCOUNT_NOT_ADMIN);
  }
  return { user }
}

const authSystemAdmin = async (request) => {
  const user = request.user;
  const roleType = user.role;
  if (roleType !== ROLES_ENUM.SYSTEM_ADMIN) {
    throw new UnauthorizedAccessException(MESSAGE.MSG_ACCOUNT_NOT_SYSTEM_ADMIN);
  }
  return { user }
}

const authEnterprise = async (request) => {
  const user = request.user;
  const roleType = user.role;
  if (roleType !== ROLES_ENUM.FISHERIES_DEPARTMENT_ADMIN && roleType !== ROLES_ENUM.LOCAL_GOVERNMENT_ADMIN) {
    const enterpriseId = user.enterprise_id;
    if (!enterpriseId) {

      throw new UnauthorizedAccessException(MESSAGE.AUTH_TOKEN_ERROR);
    }
  }
  return { user }
}

const authForUserTemporary = async (request) => {
  try {
    await request.jwtVerify();
    const baseService = request.diScope.resolve('baseService');
    const connect = baseService.DB.READ;
    const user = await connect.users.findFirst({
      where: {
        id: request.user.id,
      },
      select: {
        id: true,
        user_code: true,
        enterprise_id: true,
        phone: true,
        delete_flag: true,
        role: true,
        license_id: true,
        name: true,
        name_kana: true,
        status: true,
        account_registration_status: true,
        license: {
          select: {
            id: true,
            expiry_date: true,
          },
        },
        enterprise_type: true,
        staff_type: true,
      },
    });
    if (!user) {
      throw new UnauthorizedAccessException(MESSAGE.MSG_LIMITS_PREMISSION_ERROR);
    }

    request.user = user;
  } catch {
    APP_LOGGER.request(request);
    throw new UnauthorizedAccessException(MESSAGE.AUTH_TOKEN_ERROR);
  }
}

const authStatusRegister = async (request) => {
  try {
    if (!request.user.account_registration_status
      && request.user.role === ROLES_ENUM.NORMAL_USER) {
      throw new UnauthorizedAccessException(MESSAGE.AUTH_TOKEN_ERROR);
    }
  } catch {
    APP_LOGGER.request(request);
    throw new UnauthorizedAccessException(MESSAGE.AUTH_TOKEN_ERROR);
  }

}

// for arrival/outbound shipment feature
// only user has active status
const authUserStatus = async (request) => {
  if (request.user.status !== USER_STATUS_ENUM.ACTIVE) {
    throw new InvalidPermissionException()
  }

  return request
} 

module.exports = {
  auth,
  apiKeyVerify,
  authAdmin,
  authSystemAdmin,
  authEnterprise,
  authForUserTemporary,
  authStatusRegister,
  authUserStatus,
}