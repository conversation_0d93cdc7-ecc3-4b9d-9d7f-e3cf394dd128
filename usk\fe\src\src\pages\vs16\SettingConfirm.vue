<template>
  <q-card class="tw:rounded tw:mt-4 tw:mb-4 tw:bg-white tw:p-4">
    <div
      class="tw:my-3 tw:min-h-[60vh] tw:flex tw:flex-col tw:mb-[13rem] tw:tl:mb-[7rem]"
    >
      <div class="tw:text-m-design">以下の内容で登録します</div>

      <div class="tw:my-3 tw:flex tw:flex-col">
        <div class="tw:text-l-design tw:font-bold">アプリ設定</div>
        <div
          class="tw:flex tw:flex-col tw:tl:flex-row tw:justify-between tw:gap-4 tw:my-3"
        >
          <div class="tw:flex tw:flex-col tw:w-full tw:tl:w-[48%] tw:my-3">
            <span class="tw:text-m-design tw:mb-2">自動ログアウト</span>
            <div class="tw:p-3">
              <span class="tw:text-l-design tw:font-bold">{{
                displayData.enable_session_timeout ? '有効(推奨)' : '無効'
              }}</span>
            </div>
          </div>
          <div
            class="tw:flex tw:flex-col tw:w-full tw:tl:w-[48%] tw:my-3"
            v-if="displayData.enable_session_timeout"
          >
            <span class="tw:text-m-design tw:mb-2"
              >自動ログアウトまでの時間（1-24）</span
            >
            <div class="tw:p-3">
              <span class="tw:text-l-design tw:font-bold">{{
                displayData.session_expirytime
              }}</span>
              <span class="tw:text-m-design">時間</span>
            </div>
          </div>
        </div>

        <div
          class="tw:flex tw:flex-col tw:tl:flex-row tw:justify-between tw:gap-4 tw:my-3"
        >
          <div class="tw:flex tw:flex-col tw:w-full tw:tl:w-[48%] tw:my-3">
            <span class="tw:text-m-design tw:mb-2">本日の入荷実績を表示</span>
            <div class="tw:p-3">
              <span class="tw:text-l-design tw:font-bold">{{
                displayData.display_actual_received ? '表示' : '非表示'
              }}</span>
            </div>
          </div>
          <div class="tw:flex tw:flex-col tw:w-full tw:tl:w-[48%] tw:my-3">
            <span class="tw:text-m-design tw:mb-2"
              >QRコードスキャンの初期画面</span
            >
            <div class="tw:p-3">
              <span class="tw:text-l-design tw:font-bold">{{
                getQrScanInitLabel(displayData.qr_scan_init)
              }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="tw:my-3 tw:flex tw:flex-col">
        <div class="tw:text-l-design tw:font-bold">入出荷登録設定</div>
        <div
          class="tw:grid tw:grid-cols-1
          tw:tl:grid-cols-2 tw:justify-between tw:gap-y-4 tw:gap-x-14 tw:my-3"
        >
          <div class="tw:flex tw:flex-col tw:w-full tw:my-3"
          v-if="
            !CHECK_ROLE(
              [ROLES_ENUM.NORMAL_USER],
              [
                ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE,
                ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE,
              ],
              [STAFF_TYPE_ENUM.STAFF],
              user
            )">
            <span class="tw:text-m-design tw:mb-2">在庫の管理方法</span>
            <div class="tw:p-3">
              <span class="tw:text-l-design tw:font-bold">{{
                getInventoryControlLabel(displayData.inventory_control_type)
              }}</span>
            </div>
          </div>
          <div class="tw:flex tw:flex-col tw:w-full  tw:my-3">
            <span class="tw:text-m-design tw:mb-2"
              >デフォルトで表示する出荷先</span
            >
            <div class="tw:p-3">
              <span class="tw:text-l-design tw:font-bold">{{
                displayData.destination_name || ''
              }}</span>
            </div>
          </div>
          <div class="tw:flex tw:flex-col tw:w-full  tw:my-3">
            <span class="tw:text-m-design tw:mb-2"
              >シラスウナギの１匹のグラム換算値</span
            >
            <div class="tw:p-3">
              <span class="tw:text-l-design tw:font-bold">{{
                FORMAT_NUMBER(displayData.unit_per_gram)
              }}</span>
            </div>
          </div>
          <div class="tw:flex tw:flex-col tw:w-full tw:my-3">
            <span class="tw:text-m-design tw:mb-2"
              >出荷情報の出力方法を指定</span
            >
            <div class="tw:p-3">
              <span class="tw:text-l-design tw:font-bold">{{
                getReportTypeLabel(displayData.report_type)
              }}</span>
            </div>
          </div>
        </div>

        <div class="tw:flex tw:flex-col tw:w-full tw:my-3">
          <span class="tw:text-m-design tw:mb-2">出荷時に出荷量を表示</span>
          <div class="tw:p-3">
            <span class="tw:text-l-design tw:font-bold">{{
              displayData.display_shipment_weight ? '表示' : '非表示'
            }}</span>
          </div>
        </div>
      </div>

      <div class="tw:my-3 tw:flex tw:flex-col">
        <div class="tw:text-l-design tw:font-bold">伝票設定</div>
        <div
          class="tw:flex tw:flex-col tw:tl:flex-row tw:justify-between tw:gap-4 tw:my-3"
        >
          <div class="tw:flex tw:flex-col tw:w-full tw:tl:w-[48%] tw:my-3">
            <span class="tw:text-m-design tw:mb-2"
              >キロ単価設定（万円/kg）</span
            >
            <div class="tw:flex tw:gap-4 tw:tl:flex-row tw:flex-col">
              <div
                class="tw:p-3 tw:flex-1"
                v-for="(item, index) in displayData.price_per_kilogram"
                :key="index"
              >
                <span class="tw:text-l-design tw:font-bold">{{ FORMAT_NUMBER(item) }}</span>
              </div>
            </div>
          </div>
          <div class="tw:flex tw:flex-col tw:w-full tw:tl:w-[48%] tw:my-3">
            <span class="tw:text-m-design tw:mb-2">尾数単価設定（円/尾）</span>
            <div class="tw:flex tw:gap-4 tw:tl:flex-row tw:flex-col">
              <div
                class="tw:p-3 tw:flex-1"
                v-for="(item, index) in displayData.price_per_quantity"
                :key="index"
              >
                <span class="tw:text-l-design tw:font-bold">{{ FORMAT_NUMBER(item) }}</span>
              </div>
            </div>
          </div>
        </div>

        <div
          class="tw:flex tw:flex-col tw:tl:flex-row tw:justify-between tw:gap-4 tw:my-3"
        >
          <div class="tw:flex tw:flex-col tw:w-full tw:tl:w-[48%] tw:my-3">
            <span class="tw:text-m-design tw:mb-2">内税・外税表記</span>
            <div class="tw:p-3">
              <span class="tw:text-l-design tw:font-bold">{{
                getTaxDisplayLabel(displayData.include_tax_type)
              }}</span>
            </div>
          </div>
          <div class="tw:flex tw:flex-col tw:w-full tw:tl:w-[48%] tw:my-3">
            <span class="tw:text-m-design tw:mb-2">伝票の印刷枚数</span>
            <div class="tw:p-3">
              <span class="tw:text-l-design tw:font-bold"
                >{{ displayData.receipt_number }}</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </q-card>

  <q-footer
    elevated
    class="tw:bg-white tw:p-3
    tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
    tw:items-center tw:flex tw:justify-center tw:tl:justify-between
    tw:min-h-[91px] tw:tl:h-[6.5rem] tw:flex-col tw:gap-4 tw:tl:flex-row"
  >
    <BaseButton
      outline
      class="tw:rounded-[40px]"
      :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[23.45rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-full`"
      label="入力内容を修正する"
      @click.prevent="handleClickBack"
    />
    <BaseButton
      outline
      class="tw:rounded-[40px]"
      :class="`tw:bg-blue-3 tw:text-white tw:text-m-design tw:tl:font-bold
      tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-full`"
      label="登録する"
      @click.prevent="handleRegister"
    />
  </q-footer>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import BaseButton from 'src/components/base/vs/BaseButton.vue';
import { CHECK_ROLE, CONVERT_TEXT_SHOW_DEFAULT_SCAN_QR, FORMAT_NUMBER } from 'src/helpers/common';
import {
  ENTERPRISE_TYPE_ENUM,
  INCLUDE_TAX_TYPE_OPTIONS,
  OPTIONS_INVENTORY_CONTROL_TYPE,
  REPORT_TYPE_OPTIONS,
  ROLES_ENUM,
  STAFF_TYPE_ENUM,
} from 'src/helpers/constants';
import settingService from 'src/shared/services/setting.service';
import toast from 'src/shared/utilities/toast';
import { useAppStore } from 'src/stores/app-store';
import { useAuthStore } from 'src/stores/auth-store';
import { useConfirmFormStore } from 'src/stores/confirm-form-store';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const displayData = ref({});
const { previousRoute } = storeToRefs(useAppStore());
const { getConfirmData } = useConfirmFormStore();
const { setSettingUser } = useAppStore();
const { user } = storeToRefs(useAuthStore());

const getQrScanInitLabel = value => CONVERT_TEXT_SHOW_DEFAULT_SCAN_QR(value);

const getInventoryControlLabel = value =>
  OPTIONS_INVENTORY_CONTROL_TYPE.find(option => option.value === value)
    ?.label;

const getReportTypeLabel = value =>
  REPORT_TYPE_OPTIONS.find(option => option.value === value)?.label;

const getTaxDisplayLabel = value =>
  INCLUDE_TAX_TYPE_OPTIONS.find(option => option.value === value)?.label;

// Methods
const handleClickBack = () => {
  // Go back to previous page
  router.go(-1);
};

const handleRegister = async () => {
  // Redirect to home page
  const { destination_name, ...rest } = displayData.value;
  const payload = {
    ...rest,
  };
  const result = await settingService.updateSetting(payload);
  if (result.code === 0) {
    toast.access(result.payload.message);
    const resultSetting = await settingService.getSetting();
    setSettingUser(resultSetting.payload);
    router.push({
      name: 'home',
    });
  }
};

onMounted(() => {
  const confirmData = getConfirmData();
  if (confirmData && previousRoute.value.name === 'setting') {
    displayData.value = confirmData;
  } else {
    router.push({ name: 'setting' });
  }
});
</script>
